# AI Navigator API Schema Analysis

## Overview
This document analyzes the AI Navigator API schema to understand the expected data structure for entities and map our current enhancement outputs to the target schema.

## Key Entity Types Supported

Based on the API documentation, the system supports the following entity types:

1. **Tools** - AI tools with technical levels, frameworks, API access, pricing models
2. **Courses** - Educational content with skill levels, certificates, instructors, duration
3. **Jobs** - AI job postings with employment types, experience levels, salary ranges
4. **Hardware** - AI hardware with types, manufacturers, specifications
5. **Events** - AI events with types, dates, formats, speakers
6. **Software** - AI software with languages, platforms, licenses
7. **Research Papers** - Academic papers with research areas, authors, publication venues
8. **Agencies** - AI agencies with services, industry focus, portfolios
9. **Books** - AI books with authors, ISBN, formats
10. **Newsletters** - AI newsletters with frequency, focus areas
11. **Podcasts** - AI podcasts with hosts, topics, platforms
12. **Communities** - AI communities with types, focus areas
13. **Grants** - AI funding opportunities with amounts, deadlines

## Core Entity Schema Fields

### Base Entity Fields (Required for all types)
```json
{
  "name": "string",                    // Tool name
  "website_url": "string",             // Primary URL
  "entity_type_id": "string",          // UUID of entity type
  "short_description": "string",       // Brief description
  "description": "string",             // Full description
  "logo_url": "string",               // Logo URL
  "documentation_url": "string",       // Documentation URL
  "contact_url": "string",            // Contact URL or email
  "privacy_policy_url": "string",     // Privacy Policy URL
  "founded_year": "number",           // Year founded
  "social_links": {                   // Social media links
    "twitter": "string",
    "linkedin": "string",
    "github": "string",
    "youtube": "string",
    "facebook": "string",
    "instagram": "string",
    "discord": "string"
  },
  "category_ids": ["string"],         // Array of Category UUIDs
  "tag_ids": ["string"],              // Array of Tag UUIDs
  "feature_ids": ["string"],          // Array of Feature UUIDs
  "meta_title": "string",             // SEO meta title
  "meta_description": "string",       // SEO meta description
  "employee_count_range": "enum",     // C1_10, C11_50, C51_200, etc.
  "funding_stage": "enum",            // SEED, SERIES_A, etc.
  "location_summary": "string",       // Company location summary
  "ref_link": "string",               // Affiliate referral link
  "affiliate_status": "enum",         // NONE, APPLIED, APPROVED, REJECTED
  "scraped_review_sentiment_label": "string",  // Positive, Negative, Mixed
  "scraped_review_sentiment_score": "number",  // 0.0 to 1.0
  "scraped_review_count": "number",   // Count of scraped reviews
  "status": "enum"                    // PENDING, ACTIVE, REJECTED, etc.
}
```

### Tool-Specific Details Schema
```json
{
  "tool_details": {
    "technical_level": "enum",        // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
    "has_api": "boolean",
    "has_free_tier": "boolean",
    "frameworks": ["string"],         // TensorFlow, PyTorch, etc.
    "pricing_model": "enum",          // FREE, FREEMIUM, SUBSCRIPTION, etc.
    "price_range": "enum",            // FREE, LOW, MEDIUM, HIGH, ENTERPRISE
    "integrations": ["string"],       // GitHub, Slack, etc.
    "platforms": ["string"],          // Windows, macOS, Linux, etc.
    "target_audience": ["string"],    // Developers, Marketers, etc.
    "key_features": ["string"],       // Array of key features
    "use_cases": ["string"],          // Array of use cases
    "deployment_options": ["string"], // Cloud, On-premise, Hybrid
    "support_channels": ["string"],   // Email, Chat, Phone, Community
    "has_live_chat": "boolean",
    "customization_level": "string", // Low, Medium, High
    "learning_curve": "enum",         // LOW, MEDIUM, HIGH
    "mobile_support": "boolean",
    "demo_available": "boolean",
    "open_source": "boolean",
    "libraries": ["string"],          // OpenAI, Anthropic, Cohere
    "pricing_details": "string"       // Detailed pricing information
  }
}
```

## Enum Values

### Employee Count Ranges
- C1_10, C11_50, C51_200, C201_500, C501_1000, C1001_5000, C5001_PLUS

### Funding Stages
- SEED, PRE_SEED, SERIES_A, SERIES_B, SERIES_C, SERIES_D_PLUS, PUBLIC

### Pricing Models
- FREE, FREEMIUM, SUBSCRIPTION, PAY_PER_USE, ONE_TIME_PURCHASE, CONTACT_SALES, OPEN_SOURCE

### Price Ranges
- FREE, LOW, MEDIUM, HIGH, ENTERPRISE

### Technical Levels
- BEGINNER, INTERMEDIATE, ADVANCED, EXPERT

### Learning Curves
- LOW, MEDIUM, HIGH

### Entity Status
- PENDING, ACTIVE, REJECTED, INACTIVE, ARCHIVED, NEEDS_REVISION

### Affiliate Status
- NONE, APPLIED, APPROVED, REJECTED

## Current Enhancement Output vs Target Schema Mapping

### ✅ Fields We Generate Correctly
| Current Field | Target Field | Status |
|---------------|--------------|---------|
| `name` | `name` | ✅ Direct match |
| `website_url` | `website_url` | ✅ Direct match |
| `short_description` | `short_description` | ✅ Direct match |
| `description` | `description` | ✅ Direct match |
| `key_features` | `tool_details.key_features` | ✅ Direct match |
| `use_cases` | `tool_details.use_cases` | ✅ Direct match |
| `pricing_model` | `tool_details.pricing_model` | ✅ Direct match |
| `price_range` | `tool_details.price_range` | ✅ Direct match |
| `pricing_details` | `tool_details.pricing_details` | ✅ Direct match |
| `has_free_tier` | `tool_details.has_free_tier` | ✅ Direct match |
| `integrations` | `tool_details.integrations` | ✅ Direct match |
| `frameworks` | `tool_details.frameworks` | ✅ Direct match |
| `libraries` | `tool_details.libraries` | ✅ Direct match |
| `api_access` | `tool_details.has_api` | ✅ Mapped correctly |
| `mobile_support` | `tool_details.mobile_support` | ✅ Direct match |
| `open_source` | `tool_details.open_source` | ✅ Direct match |
| `learning_curve` | `tool_details.learning_curve` | ✅ Direct match |
| `demo_available` | `tool_details.demo_available` | ✅ Direct match |
| `support_channels` | `tool_details.support_channels` | ✅ Direct match |
| `has_live_chat` | `tool_details.has_live_chat` | ✅ Direct match |
| `customization_level` | `tool_details.customization_level` | ✅ Direct match |
| `target_audience` | `tool_details.target_audience` | ✅ Direct match |
| `deployment_options` | `tool_details.deployment_options` | ✅ Direct match |
| `supported_os` | `tool_details.platforms` | ✅ Mapped correctly |
| `founded_year` | `founded_year` | ✅ Direct match |
| `employee_count_range` | `employee_count_range` | ✅ Direct match |
| `funding_stage` | `funding_stage` | ✅ Direct match |
| `location_summary` | `location_summary` | ✅ Direct match |
| `social_links` | `social_links` | ✅ Direct match |
| `categories` | `category_ids` | ✅ Mapped via taxonomy service |
| `tags` | `tag_ids` | ✅ Mapped via taxonomy service |

### ⚠️ Fields We Generate with Issues
| Current Field | Target Field | Issue |
|---------------|--------------|-------|
| `programming_languages` | `tool_details.programming_languages` | ❌ Missing from target schema |
| `trial_available` | `tool_details.trial_available` | ❌ Missing from target schema |
| `review_sentiment_label` | `scraped_review_sentiment_label` | ⚠️ Different naming |
| `review_sentiment_score` | `scraped_review_sentiment_score` | ⚠️ Different naming |
| `review_count` | `scraped_review_count` | ⚠️ Different naming |

### ❌ Missing Fields We Don't Generate
| Target Field | Type | Priority |
|--------------|------|----------|
| `logo_url` | string | 🔴 HIGH |
| `documentation_url` | string | 🔴 HIGH |
| `contact_url` | string | 🟡 MEDIUM |
| `privacy_policy_url` | string | 🟡 MEDIUM |
| `feature_ids` | array | 🔴 HIGH |
| `meta_title` | string | 🟡 MEDIUM |
| `meta_description` | string | 🟡 MEDIUM |
| `ref_link` | string | 🟢 LOW |
| `affiliate_status` | enum | 🟢 LOW |
| `tool_details.technical_level` | enum | 🔴 HIGH |

## Enhancement Pipeline Audit

### Current Architecture Overview

Our enhancement pipeline consists of several interconnected services:

1. **Data Enrichment Service** (`data_enrichment_service.py`)
   - Primary AI-powered enhancement using Perplexity API
   - Generates comprehensive tool information from name + URL
   - Outputs structured JSON with 25+ fields

2. **Enhanced Taxonomy Service** (`enhanced_taxonomy_service.py`)
   - Maps generated categories/tags to existing taxonomy
   - Uses fuzzy matching and synonym detection
   - Handles category_ids, tag_ids mapping

3. **Type-Specific Enhancers** (`type_specific_enhancers.py`)
   - Specialized enhancement for different entity types
   - AI Tool, Research Paper, Hardware, Job, Event enhancers
   - Tailored prompts and validation schemas

4. **Enhanced Item Processor** (`enhanced_item_processor.py`)
   - Orchestrates the entire enhancement workflow
   - Builds final entity DTOs
   - Handles URL validation and data cleaning

5. **Entity Type Detector** (`entity_type_detector.py`)
   - Automatically detects entity type from name/URL/description
   - Pattern-based classification system
   - Supports 9 different entity types

### Strengths of Current Pipeline

#### ✅ Comprehensive Data Generation
- **Rich AI Enhancement**: Uses Perplexity API for thorough research
- **Multiple Data Sources**: Combines AI research with website scraping
- **Structured Output**: Generates 25+ structured fields per entity
- **Type-Specific Logic**: Tailored enhancement for different entity types

#### ✅ Smart Taxonomy Management
- **Fuzzy Matching**: Reduces duplicate categories/tags
- **Synonym Detection**: Maps similar terms to existing taxonomy
- **Confidence Scoring**: Uses similarity scores for mapping decisions
- **Missing Item Tracking**: Logs unmapped taxonomy items

#### ✅ Quality Control
- **URL Validation**: Validates and normalizes URLs
- **Data Cleaning**: Removes HTML fragments and normalizes text
- **Error Handling**: Comprehensive error handling and logging
- **Fallback Values**: Provides defaults for missing data

#### ✅ Scalability Features
- **Modular Design**: Separate services for different concerns
- **Configuration-Driven**: Uses environment variables for API keys
- **Retry Logic**: Built-in retry mechanisms for API calls
- **Rate Limiting**: Respects API rate limits

### Weaknesses and Gaps

#### ❌ Missing Critical Fields
1. **Logo URL Extraction**: No automated logo detection from websites
2. **Technical Level Assessment**: Missing BEGINNER/INTERMEDIATE/ADVANCED/EXPERT classification
3. **Feature Mapping**: No mapping to predefined feature taxonomy
4. **Documentation URL Discovery**: Limited URL discovery beyond main website

#### ❌ Data Quality Issues
1. **Inconsistent Enum Values**: Some generated values don't match target schema enums
2. **Generic Descriptions**: Sometimes generates generic "AI tool" descriptions
3. **Limited Social Link Extraction**: Basic social media link detection
4. **Pricing Information Gaps**: Inconsistent pricing detail extraction

#### ❌ Schema Alignment Issues
1. **Field Name Mismatches**: Some fields use different naming conventions
2. **Missing Required Fields**: Several target schema fields not generated
3. **Type Validation**: Limited validation against target schema types
4. **Enum Standardization**: Generated enums don't always match target values

#### ❌ Enhancement Limitations
1. **Single API Dependency**: Heavy reliance on Perplexity API
2. **Limited Website Analysis**: Basic website content extraction
3. **No Image Processing**: Cannot extract logos or screenshots
4. **Static Prompts**: Enhancement prompts not dynamically optimized