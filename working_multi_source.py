"""
Working Multi-Source AI Tool Processor
- XAI working perfectly (free credits)
- Perplexity for company data
- Toolify scraping (no Selenium)
- Alternative sources discovery
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, quote
import re
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
from config import config
import time
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkingMultiSourceProcessor:
    def __init__(self, xai_api_key: str, perplexity_api_key: str):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.xai_api_key = xai_api_key
        self.perplexity_api_key = perplexity_api_key
        
        # User agents for rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
    
    def discover_new_ai_tools(self, max_tools: int = 20) -> list:
        """Discover AI tools from multiple sources"""
        logger.info(f"🔍 Discovering AI tools from multiple sources (max: {max_tools})...")
        
        all_tools = []
        
        # Source 1: Try Toolify alternatives (AI directories)
        alt_tools = self._discover_ai_directories(max_tools // 3)
        all_tools.extend(alt_tools)
        
        # Source 2: Product Hunt AI tools
        ph_tools = self.scrape_product_hunt_ai_tools(max_tools // 3)
        all_tools.extend(ph_tools)
        
        # Source 3: AI tool aggregators and lists
        agg_tools = self._discover_ai_aggregators(max_tools // 3)
        all_tools.extend(agg_tools)
        
        # Remove duplicates
        unique_tools = self._deduplicate_tools(all_tools)
        
        logger.info(f"🎉 Total unique tools discovered: {len(unique_tools)}")
        return unique_tools[:max_tools]
    
    def _discover_ai_directories(self, max_tools: int) -> list:
        """Discover tools from AI directory sites"""
        logger.info("  🌐 Discovering from AI directories...")
        tools = []
        
        # AI directory sites (alternatives to Toolify)
        directories = [
            {
                'url': 'https://theresanaiforthat.com/trending/',
                'name': 'There\'s An AI For That'
            },
            {
                'url': 'https://aitoptools.com/',
                'name': 'AI Top Tools'
            },
            {
                'url': 'https://futurepedia.io/ai-tools',
                'name': 'Futurepedia'
            }
        ]
        
        for directory in directories:
            try:
                logger.info(f"    📦 Trying {directory['name']}...")
                dir_tools = self._scrape_ai_directory(directory['url'], directory['name'])
                tools.extend(dir_tools)
                
                if len(tools) >= max_tools:
                    break
                    
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.warning(f"    ⚠️ Failed to scrape {directory['name']}: {str(e)}")
                continue
        
        return tools[:max_tools]
    
    def _scrape_ai_directory(self, url: str, source_name: str) -> list:
        """Scrape a single AI directory"""
        tools = []
        
        try:
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Generic selectors for AI tool cards/links
                selectors = [
                    'a[href*="/tool"]',
                    'a[href*="/ai-tool"]',
                    'a[href*="/tools/"]',
                    '.tool-card a',
                    '.ai-tool a',
                    '[data-tool] a'
                ]
                
                for selector in selectors:
                    elements = soup.select(selector)
                    if elements:
                        logger.info(f"    ✅ Found {len(elements)} tools with selector: {selector}")
                        
                        for element in elements[:15]:  # Limit per selector
                            tool_data = self._extract_directory_tool(element, url, source_name)
                            if tool_data:
                                tools.append(tool_data)
                        break
                
                # If no specific selectors work, try text-based discovery
                if not tools:
                    tools = self._extract_tools_by_text_patterns(soup, url, source_name)
                    
            else:
                logger.warning(f"    ⚠️ Status {response.status_code} for {url}")
                
        except Exception as e:
            logger.warning(f"    ⚠️ Error scraping {url}: {str(e)}")
        
        return tools
    
    def _extract_directory_tool(self, element, base_url: str, source: str) -> dict:
        """Extract tool data from directory element with enhanced cleaning"""
        try:
            # Enhanced name extraction with better cleaning
            name = ""
            
            # Try multiple approaches for name extraction
            name_approaches = [
                # Approach 1: Direct text from specific selectors
                lambda: self._get_clean_text_from_selectors(element, ['h1', 'h2', 'h3', '.title', '.tool-name']),
                # Approach 2: From link text
                lambda: self._get_clean_link_text(element),
                # Approach 3: From aria-label or title attributes
                lambda: self._get_text_from_attributes(element),
            ]
            
            for approach in name_approaches:
                try:
                    name = approach()
                    if name and 3 < len(name) < 80 and self._is_valid_tool_name(name):
                        break
                except:
                    continue
            
            if not name:
                return None
            
            # Enhanced URL extraction
            url = self._extract_clean_url(element, base_url)
            
            # Enhanced description extraction
            description = self._extract_clean_description(element)
            
            # Validate final data
            if name and self._is_valid_tool_name(name):
                return {
                    'tool_name_on_directory': name,
                    'external_website_url': url or f"https://example.com/search?q={quote(name)}",
                    'description': description,
                    'category': f'{source.title()} Discovery',
                    'source': source.lower().replace(' ', '_')
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"    ⚠️ Error extracting directory tool: {str(e)}")
            return None
    
    def _get_clean_text_from_selectors(self, element, selectors: list) -> str:
        """Get clean text from specific CSS selectors"""
        for selector in selectors:
            found_elem = element.select_one(selector)
            if found_elem:
                text = found_elem.get_text().strip()
                cleaned = self._clean_tool_name(text)
                if cleaned and 3 < len(cleaned) < 80:
                    return cleaned
        return ""
    
    def _get_clean_link_text(self, element) -> str:
        """Get clean text from link elements"""
        if element.name == 'a':
            link = element
        else:
            link = element.find('a')
        
        if link:
            # Try title attribute first
            title = link.get('title', '').strip()
            if title and 3 < len(title) < 80:
                return self._clean_tool_name(title)
            
            # Then try link text
            text = link.get_text().strip()
            cleaned = self._clean_tool_name(text)
            if cleaned and 3 < len(cleaned) < 80:
                return cleaned
        
        return ""
    
    def _get_text_from_attributes(self, element) -> str:
        """Get text from HTML attributes"""
        attributes = ['aria-label', 'title', 'data-name', 'data-title']
        
        for attr in attributes:
            value = element.get(attr, '').strip()
            if value and 3 < len(value) < 80:
                cleaned = self._clean_tool_name(value)
                if cleaned:
                    return cleaned
        
        return ""
    
    def _clean_tool_name(self, raw_text: str) -> str:
        """Enhanced tool name cleaning"""
        if not raw_text:
            return ""
        
        # Remove common noise patterns
        text = raw_text.strip()
        
        # Remove pricing information
        text = re.sub(r'\$\d+[/\w]*', '', text)
        text = re.sub(r'(Free|Paid|Premium|Pro|Basic)\s*(Trial|Plan)?', '', text, flags=re.IGNORECASE)
        text = re.sub(r'(Contact for Pricing|Freemium)', '', text, flags=re.IGNORECASE)
        
        # Remove ratings and numbers at the end
        text = re.sub(r'\s*\d+\.\d+\s*$', '', text)
        text = re.sub(r'\s*\d+[KMB]?\s*$', '', text)
        
        # Remove common suffixes
        text = re.sub(r'\s*(AI|Tool|App|Software|Platform|Service|Solution)s?$', '', text, flags=re.IGNORECASE)
        
        # Remove URLs and email patterns
        text = re.sub(r'https?://[^\s]+', '', text)
        text = re.sub(r'\S+@\S+\.\S+', '', text)
        
        # Remove extra whitespace and special characters
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s\-&.\(\)]', '', text)
        
        # Clean up common patterns
        text = re.sub(r'Please login.*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'New Category.*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'Manage Category.*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'Login.*', '', text, flags=re.IGNORECASE)
        
        # Take only the first meaningful part (before description)
        parts = text.split('.')
        if len(parts) > 1 and len(parts[0]) > 3:
            text = parts[0]
        
        # Take first few words if too long
        words = text.split()
        if len(words) > 5:
            text = ' '.join(words[:5])
        
        return text.strip()
    
    def _is_valid_tool_name(self, name: str) -> bool:
        """Validate if this looks like a real tool name"""
        if not name or len(name) < 3 or len(name) > 80:
            return False
        
        # Reject if it's mostly numbers
        if len(re.findall(r'\d', name)) > len(name) * 0.5:
            return False
        
        # Reject common noise patterns
        noise_patterns = [
            r'^(new|manage|login|please|contact|free|paid)$',
            r'^\d+$',
            r'^[^\w]+$',
        ]
        
        for pattern in noise_patterns:
            if re.match(pattern, name.lower()):
                return False
        
        # Must contain at least some letters
        if not re.search(r'[a-zA-Z]', name):
            return False
        
        return True
    
    def _extract_clean_url(self, element, base_url: str) -> str:
        """Extract clean URL from element"""
        # Try to find URL from href attribute
        if element.name == 'a' and element.get('href'):
            href = element['href']
        else:
            link = element.find('a')
            href = link.get('href', '') if link else ''
        
        if href:
            # Build full URL
            if href.startswith('/'):
                return urljoin(base_url, href)
            elif href.startswith('http'):
                return href
        
        return ""
    
    def _extract_clean_description(self, element) -> str:
        """Extract clean description from element"""
        desc_selectors = ['.description', '.summary', 'p', '.excerpt', '.content']
        
        for selector in desc_selectors:
            desc_elem = element.select_one(selector)
            if desc_elem:
                desc = desc_elem.get_text().strip()
                # Clean description
                desc = re.sub(r'\s+', ' ', desc)
                if 20 < len(desc) < 300:
                    return desc
        
        return ""
    
    def _extract_tools_by_text_patterns(self, soup: BeautifulSoup, base_url: str, source: str) -> list:
        """Extract tools by looking for AI-related text patterns"""
        tools = []
        
        # Look for links with AI-related text
        ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'neural', 'gpt', 'bot', 'automation']
        
        links = soup.find_all('a', href=True)
        for link in links:
            text = link.get_text().strip().lower()
            href = link.get('href', '')
            
            # Check if this looks like an AI tool
            if any(keyword in text for keyword in ai_keywords) and len(text) > 5 and len(text) < 80:
                
                # Build full URL
                if href.startswith('/'):
                    full_url = urljoin(base_url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue
                
                # Avoid internal navigation links
                if any(skip in href.lower() for skip in ['#', 'javascript:', 'mailto:', 'tel:']):
                    continue
                
                tools.append({
                    'tool_name_on_directory': text.title(),
                    'external_website_url': full_url,
                    'description': '',
                    'category': 'AI Pattern Discovery',
                    'source': source.lower().replace(' ', '_')
                })
                
                if len(tools) >= 10:  # Limit fallback results
                    break
        
        return tools
    
    def scrape_product_hunt_ai_tools(self, max_tools: int = 15) -> list:
        """Enhanced Product Hunt AI tools scraping"""
        logger.info("  🦄 Discovering from Product Hunt AI...")
        tools = []
        
        try:
            # Product Hunt AI-related searches
            ph_searches = [
                "https://www.producthunt.com/search?q=AI+tool",
                "https://www.producthunt.com/search?q=artificial+intelligence",
                "https://www.producthunt.com/search?q=machine+learning",
                "https://www.producthunt.com/topics/artificial-intelligence"
            ]
            
            for search_url in ph_searches:
                try:
                    headers = {'User-Agent': random.choice(self.user_agents)}
                    response = requests.get(search_url, headers=headers, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Look for product cards or links
                        selectors = [
                            'a[href*="/posts/"]',
                            '[data-test*="post"] a',
                            '.product-item a',
                            'h3 a, h2 a'
                        ]
                        
                        for selector in selectors:
                            elements = soup.select(selector)
                            if elements:
                                logger.info(f"    ✅ Found {len(elements)} PH products")
                                
                                for element in elements[:10]:
                                    tool_data = self._extract_ph_tool(element)
                                    if tool_data:
                                        tools.append(tool_data)
                                break
                    
                    if len(tools) >= max_tools:
                        break
                        
                    time.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"    ⚠️ Error with PH search: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.warning(f"  ⚠️ Product Hunt scraping failed: {str(e)}")
        
        return tools[:max_tools]
    
    def _extract_ph_tool(self, element) -> dict:
        """Extract tool from Product Hunt element"""
        try:
            # Get product name
            name = element.get_text().strip()
            if not name or len(name) < 3:
                return None
            
            # Get Product Hunt URL
            href = element.get('href', '')
            if href.startswith('/'):
                ph_url = f"https://www.producthunt.com{href}"
            else:
                ph_url = href
            
            # Try to get actual website from Product Hunt page
            actual_url = self._resolve_ph_to_website(ph_url, name)
            
            return {
                'tool_name_on_directory': name,
                'external_website_url': actual_url or ph_url,
                'description': '',
                'category': 'Product Hunt Discovery',
                'source': 'product_hunt'
            }
            
        except Exception as e:
            logger.warning(f"    ⚠️ Error extracting PH tool: {str(e)}")
            return None
    
    def _resolve_ph_to_website(self, ph_url: str, tool_name: str) -> str:
        """Try to resolve Product Hunt URL to actual website"""
        try:
            # Simple approach: construct likely website URLs
            name_clean = re.sub(r'[^a-zA-Z0-9]', '', tool_name.lower())
            potential_urls = [
                f"https://{name_clean}.com",
                f"https://www.{name_clean}.com",
                f"https://{name_clean}.ai",
                f"https://www.{name_clean}.ai",
                f"https://{name_clean}.io",
            ]
            
            # Test which URL responds
            for url in potential_urls:
                try:
                    response = requests.head(url, timeout=5)
                    if response.status_code == 200:
                        return url
                except:
                    continue
            
            return ph_url  # Fallback to Product Hunt URL
            
        except:
            return ph_url
    
    def _discover_ai_aggregators(self, max_tools: int) -> list:
        """Discover from AI tool aggregator sites"""
        logger.info("  📋 Discovering from AI aggregators...")
        tools = []
        
        # AI tool listing sites
        aggregators = [
            "https://aitools.fyi/",
            "https://www.marktechpost.com/ai-tool-categories/",
            "https://www.aitoolnet.com/",
        ]
        
        for agg_url in aggregators:
            try:
                headers = {'User-Agent': random.choice(self.user_agents)}
                response = requests.get(agg_url, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Look for tool links
                    links = soup.find_all('a', href=True)
                    for link in links:
                        text = link.get_text().strip()
                        href = link.get('href', '')
                        
                        # Filter for likely AI tools
                        if (len(text) > 5 and len(text) < 50 and 
                            any(word in text.lower() for word in ['ai', 'tool', 'assistant', 'generator'])):
                            
                            if href.startswith('http') and not any(skip in href for skip in [agg_url, 'mailto', 'tel']):
                                tools.append({
                                    'tool_name_on_directory': text,
                                    'external_website_url': href,
                                    'description': '',
                                    'category': 'Aggregator Discovery',
                                    'source': 'aggregator'
                                })
                
                time.sleep(2)
                
                if len(tools) >= max_tools:
                    break
                    
            except Exception as e:
                logger.warning(f"    ⚠️ Error with aggregator: {str(e)}")
                continue
        
        return tools[:max_tools]
    
    def _deduplicate_tools(self, tools: list) -> list:
        """Remove duplicate tools"""
        seen = set()
        unique_tools = []
        
        for tool in tools:
            # Create a key for deduplication
            name = tool.get('tool_name_on_directory', '').lower().strip()
            url = tool.get('external_website_url', '').lower().strip()
            
            # Simple dedup key
            key = f"{name}|{urlparse(url).netloc if url else ''}"
            
            if key not in seen and name and len(name) > 2:
                seen.add(key)
                unique_tools.append(tool)
        
        return unique_tools
    
    def process_tool(self, tool_data: dict) -> bool:
        """Process a single tool with enhanced XAI + Perplexity"""
        tool_name = tool_data.get('tool_name_on_directory', '')
        external_url = tool_data.get('external_website_url', '')
        source = tool_data.get('source', 'unknown')
        
        logger.info(f"🚀 Processing: {tool_name} (from {source})")
        
        try:
            # Step 1: Resolve actual website URL
            actual_url = self._resolve_actual_url(external_url, source)
            logger.info(f"  🔍 Resolved URL: {actual_url}")
            
            # Step 2: Check if entity exists
            existing_entity = self._find_existing_entity(tool_name, actual_url)
            
            # Step 3: Scrape website
            website_data = self._scrape_website(actual_url)
            
            # Step 4: Enhanced AI pipeline - XAI working + Perplexity
            enhanced_data = self._enhance_with_working_ai(tool_name, website_data, actual_url)
            
            # Step 5: Build entity
            entity_data = self._build_validated_entity(tool_name, actual_url, website_data, enhanced_data)
            
            # Step 6: Create or update
            if existing_entity:
                result = self._update_entity(existing_entity['id'], entity_data)
                action = "Updated"
            else:
                result = self._create_entity(entity_data)
                action = "Created"
            
            if result:
                logger.info(f"  ✅ {action} entity: {tool_name}")
                return True
            else:
                logger.error(f"  ❌ Failed to {action.lower()} entity: {tool_name}")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ Error processing {tool_name}: {str(e)}")
            return False
    
    def _enhance_with_working_ai(self, tool_name: str, website_data: dict, url: str) -> dict:
        """AI enhancement with working XAI + Perplexity"""
        
        # Primary: Use XAI (we confirmed it's working)
        xai_data = self._try_working_xai(tool_name, website_data, url)
        
        if xai_data and xai_data.get('short_description'):
            logger.info(f"  🤖 XAI successful, adding company data...")
            # Add company data from Perplexity
            company_data = self._get_company_data_perplexity(tool_name, url)
            xai_data.update(company_data)
            return xai_data
        else:
            # Fallback to Perplexity
            logger.info(f"  🔄 XAI failed, using Perplexity...")
            return self._try_perplexity_enhancement(tool_name, website_data, url)
    
    def _try_working_xai(self, tool_name: str, website_data: dict, url: str) -> dict:
        """XAI enhancement with working key and proper JSON parsing"""
        try:
            content_summary = f"""
Tool: {tool_name}
Website: {url}
Title: {website_data.get('title', '')}
Description: {website_data.get('meta_description', '')}
Content: {website_data.get('content_text', '')[:1000]}
"""
            
            prompt = f"""
Analyze this tool and extract information. Return ONLY valid JSON:

{content_summary}

{{
  "short_description": "Specific description in 1 sentence (max 150 chars)",
  "description": "Detailed description in 2-3 sentences (max 400 chars)",
  "key_features": ["feature1", "feature2", "feature3"],
  "use_cases": ["use_case1", "use_case2"],
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3"],
  "pricing_model": "FREEMIUM",
  "target_audience": ["audience1", "audience2"]
}}
"""

            response = requests.post(
                "https://api.x.ai/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.xai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "grok-3-beta",
                    "messages": [
                        {"role": "system", "content": "You are an expert at analyzing websites. Always respond with valid JSON only. No markdown formatting or explanations."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 1000
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Clean any markdown formatting
                if content.startswith('```json'):
                    content = content.replace('```json', '').replace('```', '').strip()
                elif content.startswith('```'):
                    content = content.replace('```', '').strip()
                
                # Parse JSON
                try:
                    enhanced_data = json.loads(content)
                    logger.info(f"  🤖 XAI enhanced {tool_name} successfully")
                    return enhanced_data
                except json.JSONDecodeError:
                    # Try to extract JSON from response
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = content[json_start:json_end]
                        enhanced_data = json.loads(json_str)
                        logger.info(f"  🤖 XAI enhanced {tool_name} (extracted JSON)")
                        return enhanced_data
                    else:
                        logger.warning(f"  ⚠️ XAI returned invalid JSON: {content[:100]}")
            else:
                logger.warning(f"  ⚠️ XAI failed with status: {response.status_code}")
            
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ XAI error: {str(e)}")
            return {}
    
    def _get_company_data_perplexity(self, tool_name: str, url: str) -> dict:
        """Get company data from Perplexity (minimal cost)"""
        try:
            prompt = f"""
Research company data for "{tool_name}" (website: {url}). Return JSON:

{{
  "founded_year": 2023,
  "employee_count_range": "C1_10",
  "funding_stage": "SEED",
  "location_summary": "City, Country"
}}
"""

            response = requests.post(
                "https://api.perplexity.ai/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.perplexity_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "llama-3.1-sonar-small-128k-online",
                    "messages": [
                        {"role": "system", "content": "Provide only factual company data in JSON format."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 200
                },
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    company_data = json.loads(content[json_start:json_end])
                    logger.info(f"  💰 Added company data via Perplexity")
                    return company_data
            
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ Perplexity company data error: {str(e)}")
            return {}
    
    # Include all the helper methods from working_processor.py
    def _resolve_actual_url(self, url: str, source: str) -> str:
        if source == 'futuretools':
            return self._resolve_futuretools_url(url)
        return url
    
    def _resolve_futuretools_url(self, futuretools_url: str) -> str:
        try:
            response = requests.get(futuretools_url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            meta_refresh = re.search(r'<meta[^>]*http-equiv=["\']refresh["\'][^>]*content=["\'][^>]*url=([^"\'>\s]+)', response.text, re.IGNORECASE)
            if meta_refresh:
                url = meta_refresh.group(1)
                url = re.sub(r'[\?&]ref=[^&]*', '', url)
                url = re.sub(r'[\?&]utm_[^&]*=[^&]*', '', url)
                return url
            
            return futuretools_url
            
        except Exception as e:
            logger.warning(f"Error resolving URL: {str(e)}")
            return futuretools_url
    
    def _find_existing_entity(self, name: str, website_url: str) -> dict:
        try:
            response = requests.get(
                f"{self.client.base_url}/entities",
                headers=self.client._get_headers(),
                params={"search": name, "limit": 10},
                timeout=10
            )
            
            if response.status_code == 200:
                entities = response.json()
                if isinstance(entities, dict) and 'data' in entities:
                    entities = entities['data']
                
                for entity in entities:
                    if (entity.get('name', '').lower() == name.lower() or 
                        entity.get('website_url', '') == website_url):
                        logger.info(f"  🔄 Found existing entity: {entity.get('id')}")
                        return entity
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking existing entity: {str(e)}")
            return None
    
    def _scrape_website(self, url: str) -> dict:
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': random.choice(self.user_agents)
            })
            
            if response.status_code != 200:
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            data = {
                'title': self._get_title(soup),
                'meta_description': self._get_meta_description(soup),
                'logo_url': self._extract_logo(soup, url),
                'social_links': self._extract_social_links(response.text),
                'content_text': soup.get_text()[:2000],
                'domain': urlparse(url).netloc
            }
            
            logger.info(f"  📊 Scraped website successfully")
            return data
            
        except Exception as e:
            logger.warning(f"Error scraping website: {str(e)}")
            return {}
    
    def _try_perplexity_enhancement(self, tool_name: str, website_data: dict, url: str) -> dict:
        try:
            content_summary = f"""
Tool: {tool_name}
Website: {url}
Title: {website_data.get('title', '')}
Description: {website_data.get('meta_description', '')}
Content: {website_data.get('content_text', '')[:800]}
"""
            
            prompt = f"""
Analyze this tool and return JSON:

{content_summary}

{{
  "short_description": "Brief description (max 150 chars)",
  "description": "Detailed description (max 400 chars)",
  "key_features": ["feature1", "feature2", "feature3"],
  "use_cases": ["use_case1", "use_case2"],
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3"],
  "pricing_model": "FREEMIUM",
  "target_audience": ["audience1", "audience2"]
}}
"""

            response = requests.post(
                "https://api.perplexity.ai/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.perplexity_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "llama-3.1-sonar-small-128k-online",
                    "messages": [
                        {"role": "system", "content": "Provide JSON format responses only."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 800
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    enhanced_data = json.loads(content[json_start:json_end])
                    logger.info(f"  💰 Perplexity enhanced {tool_name}")
                    return enhanced_data
            
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ Perplexity error: {str(e)}")
            return {}
    
    def _build_validated_entity(self, name: str, url: str, website_data: dict, enhanced_data: dict) -> dict:
        entity_type = self.entity_detector.detect_entity_type(name, url)
        entity_type_id = self._get_entity_type_id(entity_type)
        
        category_ids = self.taxonomy_service.map_categories(enhanced_data.get('categories', []))
        tag_ids = self.taxonomy_service.map_tags(enhanced_data.get('tags', []))
        feature_ids = self.taxonomy_service.map_features(enhanced_data.get('key_features', []))
        
        # UUID validation
        def is_valid_uuid(uuid_string):
            try:
                import uuid
                uuid.UUID(uuid_string)
                return True
            except (ValueError, TypeError):
                return False
        
        category_ids = [id for id in category_ids if is_valid_uuid(id)]
        tag_ids = [id for id in tag_ids if is_valid_uuid(id)]
        feature_ids = [id for id in feature_ids if is_valid_uuid(id)]
        
        if not category_ids and self.taxonomy_service.categories_map:
            first_cat = list(self.taxonomy_service.categories_map.values())[0]
            category_ids = [first_cat['id']]
        
        if not tag_ids:
            default_tags = self.taxonomy_service.map_tags(['AI-Powered'])
            if default_tags:
                tag_ids = [id for id in default_tags if is_valid_uuid(id)]
        
        short_desc = str(enhanced_data.get('short_description', f"{name} - AI-powered solution"))[:150]
        long_desc = str(enhanced_data.get('description', f"{name} provides professional AI capabilities."))[:400]
        
        entity = {
            'name': name,
            'website_url': url,
            'entity_type_id': entity_type_id,
            'short_description': short_desc,
            'description': long_desc,
            'category_ids': category_ids,
            'tag_ids': tag_ids if tag_ids else [],
            'feature_ids': feature_ids,
            'meta_title': f"{name} | AI Navigator",
            'meta_description': short_desc,
            'ref_link': url,
            'affiliate_status': 'NONE',
            'status': 'PENDING'
        }
        
        if website_data.get('logo_url'):
            entity['logo_url'] = website_data['logo_url']
        
        if website_data.get('social_links'):
            entity['social_links'] = website_data['social_links']
        
        # Validate and add optional fields with proper enums
        founded_year = enhanced_data.get('founded_year')
        if founded_year and isinstance(founded_year, int) and 1950 <= founded_year <= 2025:
            entity['founded_year'] = founded_year
        
        employee_count = enhanced_data.get('employee_count_range')
        valid_employee_counts = ['C1_10', 'C11_50', 'C51_200', 'C201_500', 'C501_1000', 'C1001_5000', 'C5001_PLUS']
        if employee_count in valid_employee_counts:
            entity['employee_count_range'] = employee_count
        
        funding_stage = enhanced_data.get('funding_stage')
        valid_funding_stages = ['SEED', 'PRE_SEED', 'SERIES_A', 'SERIES_B', 'SERIES_C', 'SERIES_D_PLUS', 'PUBLIC']
        if funding_stage in valid_funding_stages:
            entity['funding_stage'] = funding_stage
        
        location_summary = enhanced_data.get('location_summary')
        if location_summary and isinstance(location_summary, str) and len(location_summary) > 2:
            entity['location_summary'] = location_summary
        
        entity['tool_details'] = {
            'learning_curve': 'MEDIUM',
            'key_features': enhanced_data.get('key_features', ['Professional Tools'])[:10],
            'has_free_tier': enhanced_data.get('has_free_tier', True),
            'use_cases': enhanced_data.get('use_cases', ['Business Applications'])[:5],
            'pricing_model': enhanced_data.get('pricing_model', 'FREEMIUM'),
            'target_audience': enhanced_data.get('target_audience', ['Business Professionals'])[:5],
            'mobile_support': enhanced_data.get('mobile_support', False),
            'api_access': enhanced_data.get('api_access', False),
            'customization_level': 'Medium',
            'trial_available': True,
            'demo_available': False,
            'open_source': False,
            'support_channels': ['Email', 'Documentation'],
            'integrations': enhanced_data.get('integrations', ['Standard APIs'])[:8],
            'supported_os': enhanced_data.get('supported_os', ['Web Browser'])[:5]
        }
        
        return entity
    
    def _get_entity_type_id(self, entity_type_slug: str) -> str:
        try:
            response = requests.get(
                f"{self.client.base_url}/entity-types",
                headers=self.client._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entity_types = response.json()
                for et in entity_types:
                    if et.get('slug') == entity_type_slug:
                        return et.get('id')
            
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"
            
        except Exception as e:
            logger.error(f"Error getting entity type ID: {str(e)}")
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"
    
    def _create_entity(self, entity_data: dict) -> bool:
        try:
            result = self.client.create_entity(entity_data)
            return bool(result)
        except Exception as e:
            logger.error(f"Error creating entity: {str(e)}")
            return False
    
    def _update_entity(self, entity_id: str, entity_data: dict) -> bool:
        try:
            update_data = entity_data.copy()
            update_data.pop('name', None)
            
            response = requests.patch(
                f"{self.client.base_url}/entities/{entity_id}",
                headers=self.client._get_headers(),
                json=update_data,
                timeout=30
            )
            
            return response.status_code in [200, 201]
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False
    
    # Helper methods
    def _get_title(self, soup):
        title = soup.find('title')
        return title.get_text().strip() if title else ""
    
    def _get_meta_description(self, soup):
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '').strip() if meta_desc else ""
    
    def _extract_logo(self, soup, base_url):
        selectors = [
            'img[alt*="logo" i]', '.logo img', 'header img', '.navbar img',
            'img[src*="logo" i]', 'link[rel="apple-touch-icon"]', 'link[rel="icon"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                src = element.get('src') or element.get('href')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    if src.startswith('http'):
                        return src
        
        domain = urlparse(base_url).netloc
        return f"https://www.google.com/s2/favicons?sz=128&domain={domain}"
    
    def _extract_social_links(self, page_text):
        patterns = {
            'twitter': r'twitter\.com/([a-zA-Z0-9_]{1,15})',
            'linkedin': r'linkedin\.com/company/([a-zA-Z0-9\-]{1,50})',
            'github': r'github\.com/([a-zA-Z0-9\-_]{1,39})'
        }
        
        social_links = {}
        for platform, pattern in patterns.items():
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                handle = matches[0]
                if handle and not any(skip in handle.lower() for skip in ['home', 'login', 'about']):
                    social_links[platform] = handle
        
        return social_links

def main():
    """Test multi-source processor with working XAI"""
    
    # Use configuration from environment variables
    processor = WorkingMultiSourceProcessor(config.api.xai_api_key, config.api.perplexity_api_key)
    
    logger.info("🚀 Testing multi-source AI tool discovery with working XAI...")
    
    # Discover new tools from multiple sources
    new_tools = processor.discover_new_ai_tools(max_tools=10)
    
    if new_tools:
        logger.info(f"✅ Discovered {len(new_tools)} new tools")
        
        # Process first 5 tools
        success_count = 0
        for i, tool in enumerate(new_tools[:5]):
            logger.info(f"\n{'='*60}")
            logger.info(f"Processing discovered tool {i+1}/5")
            
            success = processor.process_tool(tool)
            if success:
                success_count += 1
            
            time.sleep(3)  # Be respectful
        
        logger.info(f"\n🎉 MULTI-SOURCE SUCCESS RATE: {success_count}/5 tools ({(success_count/5)*100:.1f}%)")
        logger.info(f"💰 Estimated cost: ${success_count * 0.05:.2f}")
    else:
        logger.warning("❌ No new tools discovered from multi-sources")

if __name__ == "__main__":
    main()