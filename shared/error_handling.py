"""
Comprehensive Error Handling
Standardized error handling patterns and utilities
"""

import logging
import traceback
import functools
from typing import Any, Callable, Dict, Optional, Type, Union
from enum import Enum
import time

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ScraperError(Exception):
    """Base exception for scraper-related errors"""
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM, context: Dict[str, Any] = None):
        super().__init__(message)
        self.severity = severity
        self.context = context or {}
        self.timestamp = time.time()

class APIError(ScraperError):
    """API-related errors"""
    def __init__(self, message: str, api_name: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.api_name = api_name
        self.status_code = status_code

class ValidationError(ScraperError):
    """Data validation errors"""
    def __init__(self, message: str, field: str = None, value: Any = None, **kwargs):
        super().__init__(message, **kwargs)
        self.field = field
        self.value = value

class NetworkError(ScraperError):
    """Network-related errors"""
    def __init__(self, message: str, url: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.url = url

class ProcessingError(ScraperError):
    """Data processing errors"""
    def __init__(self, message: str, stage: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.stage = stage

class ErrorHandler:
    """Centralized error handling and logging"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.error_counts = {}
        self.error_history = []

    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """Handle and log errors with context"""
        error_type = type(error).__name__

        # Count errors
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1

        # Create error record
        error_record = {
            'type': error_type,
            'message': str(error),
            'timestamp': time.time(),
            'context': context or {},
            'traceback': traceback.format_exc()
        }

        # Add custom error attributes if available
        if isinstance(error, ScraperError):
            error_record.update({
                'severity': error.severity.value,
                'custom_context': error.context
            })

            if isinstance(error, APIError):
                error_record.update({
                    'api_name': error.api_name,
                    'status_code': error.status_code
                })
            elif isinstance(error, ValidationError):
                error_record.update({
                    'field': error.field,
                    'value': error.value
                })
            elif isinstance(error, NetworkError):
                error_record.update({
                    'url': error.url
                })
            elif isinstance(error, ProcessingError):
                error_record.update({
                    'stage': error.stage
                })

        # Store in history
        self.error_history.append(error_record)

        # Log based on severity
        if isinstance(error, ScraperError):
            if error.severity == ErrorSeverity.CRITICAL:
                self.logger.critical(f"{error_type}: {error}", extra=error_record)
            elif error.severity == ErrorSeverity.HIGH:
                self.logger.error(f"{error_type}: {error}", extra=error_record)
            elif error.severity == ErrorSeverity.MEDIUM:
                self.logger.warning(f"{error_type}: {error}", extra=error_record)
            else:
                self.logger.info(f"{error_type}: {error}", extra=error_record)
        else:
            self.logger.error(f"{error_type}: {error}", extra=error_record)

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors encountered"""
        return {
            'total_errors': len(self.error_history),
            'error_counts': self.error_counts.copy(),
            'recent_errors': self.error_history[-10:] if self.error_history else []
        }

def retry_on_error(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
) -> Callable:
    """Decorator for retrying functions on specific exceptions"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff_factor ** attempt)
                        time.sleep(wait_time)
                        continue
                    else:
                        raise last_exception

            return None
        return wrapper
    return decorator

def safe_execute(
    func: Callable,
    default_return: Any = None,
    error_handler: Optional[ErrorHandler] = None,
    context: Dict[str, Any] = None
) -> Any:
    """Safely execute a function with error handling"""
    try:
        return func()
    except Exception as e:
        if error_handler:
            error_handler.handle_error(e, context)
        else:
            logging.getLogger(__name__).error(f"Error in {func.__name__}: {str(e)}")
        return default_return

def validate_required_fields(data: Dict[str, Any], required_fields: list) -> None:
    """Validate that required fields are present and not empty"""
    missing_fields = []
    empty_fields = []

    for field in required_fields:
        if field not in data:
            missing_fields.append(field)
        elif not data[field] or (isinstance(data[field], str) and not data[field].strip()):
            empty_fields.append(field)

    if missing_fields:
        raise ValidationError(
            f"Missing required fields: {', '.join(missing_fields)}",
            severity=ErrorSeverity.HIGH
        )

    if empty_fields:
        raise ValidationError(
            f"Empty required fields: {', '.join(empty_fields)}",
            severity=ErrorSeverity.MEDIUM
        )