"""
Centralized Logging Configuration
Provides standardized logging setup for the entire application
"""

import logging
import logging.handlers
import os
import sys
from typing import Optional, Dict, Any
from datetime import datetime
from config import config

class ColoredFormatter(logging.Formatter):
    """Colored console formatter for better readability"""

    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }

    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"

        return super().format(record)

class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter for production logging"""

    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        # Add extra fields from the record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value

        return str(log_entry)

def setup_logging(
    name: Optional[str] = None,
    level: Optional[str] = None,
    log_to_file: bool = True,
    log_to_console: bool = True,
    structured: bool = False
) -> logging.Logger:
    """
    Setup comprehensive logging configuration

    Args:
        name: Logger name (defaults to calling module)
        level: Log level (defaults to config.server.log_level)
        log_to_file: Whether to log to file
        log_to_console: Whether to log to console
        structured: Whether to use structured JSON logging

    Returns:
        Configured logger instance
    """

    # Determine logger name
    if name is None:
        frame = sys._getframe(1)
        name = frame.f_globals.get('__name__', 'unknown')

    # Get or create logger
    logger = logging.getLogger(name)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Set level
    log_level = level or config.server.log_level
    logger.setLevel(getattr(logging, log_level.upper()))

    # Create formatters
    if structured:
        formatter = StructuredFormatter()
    else:
        # Standard format
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        formatter = logging.Formatter(format_string)

        # Colored formatter for console
        colored_formatter = ColoredFormatter(format_string)

    # Console handler
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)

        if structured:
            console_handler.setFormatter(formatter)
        else:
            console_handler.setFormatter(colored_formatter)

        logger.addHandler(console_handler)

    # File handler
    if log_to_file:
        # Ensure log directory exists
        log_dir = config.log_dir
        os.makedirs(log_dir, exist_ok=True)

        # Main log file
        log_file = os.path.join(log_dir, f"{name.replace('.', '_')}.log")
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Error log file (errors and above only)
        error_log_file = os.path.join(log_dir, f"{name.replace('.', '_')}_errors.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)

    # Prevent propagation to root logger
    logger.propagate = False

    return logger

def get_logger(name: str = None) -> logging.Logger:
    """Get a configured logger instance"""
    if name is None:
        frame = sys._getframe(1)
        name = frame.f_globals.get('__name__', 'unknown')

    return setup_logging(name)