"""
Base Processor Class
Shared functionality for all AI tool processors
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, quote
import re
from typing import Dict, List, Optional, Any
import time
import random
from abc import ABC, abstractmethod

from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
from config import config

class BaseProcessor(ABC):
    """Base class for all AI tool processors"""

    def __init__(self, xai_api_key: Optional[str] = None, perplexity_api_key: Optional[str] = None):
        """Initialize the processor with API keys"""
        self.xai_api_key = xai_api_key or config.api.xai_api_key
        self.perplexity_api_key = perplexity_api_key or config.api.perplexity_api_key

        # Validate API keys
        if not self.xai_api_key:
            raise ValueError("XAI API key is required")
        if not self.perplexity_api_key:
            raise ValueError("Perplexity API key is required")

        # Initialize services
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()

        # Setup logging
        self.logger = logging.getLogger(self.__class__.__name__)

        # Common headers for web requests
        self.headers = {
            'User-Agent': config.scraping.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

    def make_request(self, url: str, timeout: int = None) -> Optional[requests.Response]:
        """Make a safe HTTP request with error handling and rate limiting"""
        try:
            # Rate limiting
            delay = random.uniform(*config.scraping.delay_range)
            time.sleep(delay)

            timeout = timeout or config.scraping.timeout
            response = requests.get(url, headers=self.headers, timeout=timeout)
            response.raise_for_status()
            return response

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed for {url}: {str(e)}")
            return None

    def extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            return urlparse(url).netloc.lower()
        except Exception:
            return ""

    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())

        # Remove common unwanted characters
        text = re.sub(r'[^\w\s\-\.\,\!\?\:\;\(\)\/\&\@\#\$\%\+\=]', '', text)

        return text

    def extract_social_links(self, soup, base_url: str) -> Dict[str, str]:
        """Extract social media links from page"""
        social_links = {}

        # Social media patterns
        social_patterns = {
            'twitter': [
                r'twitter\.com/([a-zA-Z0-9_]{1,15})(?:\?|$|/)',
                r'x\.com/([a-zA-Z0-9_]{1,15})(?:\?|$|/)',
            ],
            'linkedin': [
                r'linkedin\.com/company/([a-zA-Z0-9\-]{1,100})(?:\?|$|/)',
                r'linkedin\.com/in/([a-zA-Z0-9\-]{1,100})(?:\?|$|/)',
            ],
            'github': [
                r'github\.com/([a-zA-Z0-9\-_.]{1,39})(?:\?|$|/)',
            ],
        }

        # Find all links
        links = soup.find_all('a', href=True)

        for link in links:
            href = link.get('href', '')
            if not href:
                continue

            # Convert relative URLs to absolute
            if href.startswith('/'):
                href = urljoin(base_url, href)

            # Check against social patterns
            for platform, patterns in social_patterns.items():
                for pattern in patterns:
                    match = re.search(pattern, href, re.IGNORECASE)
                    if match:
                        handle = match.group(1)
                        # Skip common false positives
                        exclusions = {
                            'twitter': ['home', 'login', 'signup', 'privacy', 'terms'],
                            'linkedin': ['login', 'signup', 'help', 'legal', 'privacy'],
                            'github': ['login', 'join', 'pricing', 'features'],
                        }

                        if handle.lower() not in exclusions.get(platform, []):
                            social_links[platform] = handle
                            break

        return social_links

    def call_xai_api(self, prompt: str, max_tokens: int = 1000) -> Dict[str, Any]:
        """Call XAI API with error handling"""
        try:
            headers = {
                'Authorization': f'Bearer {self.xai_api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'messages': [{'role': 'user', 'content': prompt}],
                'model': 'grok-beta',
                'max_tokens': max_tokens,
                'temperature': 0.1
            }

            response = requests.post(
                'https://api.x.ai/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']

                # Try to parse as JSON
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    return {'raw_content': content}
            else:
                self.logger.error(f"XAI API error: {response.status_code} - {response.text}")
                return {}

        except Exception as e:
            self.logger.error(f"XAI API call failed: {str(e)}")
            return {}

    def call_perplexity_api(self, prompt: str, max_tokens: int = 1000) -> Dict[str, Any]:
        """Call Perplexity API with error handling"""
        try:
            headers = {
                'Authorization': f'Bearer {self.perplexity_api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': 'llama-3.1-sonar-small-128k-online',
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': max_tokens,
                'temperature': 0.1
            }

            response = requests.post(
                'https://api.perplexity.ai/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']

                # Try to parse as JSON
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    return {'raw_content': content}
            else:
                self.logger.error(f"Perplexity API error: {response.status_code} - {response.text}")
                return {}

        except Exception as e:
            self.logger.error(f"Perplexity API call failed: {str(e)}")
            return {}

    @abstractmethod
    def process_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a list of tools - must be implemented by subclasses"""
        pass