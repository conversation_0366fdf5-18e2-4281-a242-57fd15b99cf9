"""
Dedicated Toolify.ai Scraper
Test with 50 tools, then expand to other sources
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, quote
import re
from ai_navigator_client import AINavigator<PERSON>lient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
import time
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ToolifyScraperTester:
    def __init__(self, xai_api_key: str, perplexity_api_key: str):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.xai_api_key = xai_api_key
        self.perplexity_api_key = perplexity_api_key
        
        # Enhanced user agents with more variety
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
        ]
    
    def test_toolify_scraping(self, max_tools: int = 50) -> list:
        """Comprehensive test of Toolify.ai scraping"""
        logger.info(f"🔍 Testing Toolify.ai scraping for {max_tools} tools...")
        
        all_tools = []
        
        # Try multiple Toolify endpoints and approaches
        toolify_strategies = [
            self._try_toolify_main_page,
            self._try_toolify_categories,
            self._try_toolify_search,
            self._try_toolify_browse,
            self._try_toolify_sitemap
        ]
        
        for i, strategy in enumerate(toolify_strategies):
            try:
                logger.info(f"  📦 Strategy {i+1}/5: {strategy.__name__}")
                tools = strategy(max_tools - len(all_tools))
                
                if tools:
                    all_tools.extend(tools)
                    logger.info(f"  ✅ Found {len(tools)} tools with {strategy.__name__}")
                    
                    if len(all_tools) >= max_tools:
                        break
                else:
                    logger.warning(f"  ⚠️ No tools found with {strategy.__name__}")
                
                # Respectful delay between strategies
                time.sleep(random.uniform(3, 6))
                
            except Exception as e:
                logger.warning(f"  ❌ Strategy {strategy.__name__} failed: {str(e)}")
                continue
        
        # Remove duplicates
        unique_tools = self._deduplicate_tools(all_tools)
        
        logger.info(f"🎉 Toolify test complete: {len(unique_tools)} unique tools found")
        return unique_tools[:max_tools]
    
    def _try_toolify_main_page(self, max_tools: int) -> list:
        """Try scraping Toolify main page"""
        return self._scrape_toolify_url("https://toolify.ai/", max_tools)
    
    def _try_toolify_categories(self, max_tools: int) -> list:
        """Try scraping Toolify categories"""
        tools = []
        
        # Common AI tool categories on Toolify
        categories = [
            "ai-tools",
            "writing",
            "image-generation", 
            "productivity",
            "chatbots",
            "video",
            "audio",
            "code",
            "design"
        ]
        
        for category in categories:
            if len(tools) >= max_tools:
                break
                
            try:
                url = f"https://toolify.ai/{category}"
                category_tools = self._scrape_toolify_url(url, max_tools - len(tools))
                tools.extend(category_tools)
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.warning(f"    ⚠️ Category {category} failed: {str(e)}")
                continue
        
        return tools
    
    def _try_toolify_search(self, max_tools: int) -> list:
        """Try Toolify search functionality"""
        tools = []
        
        # Search terms for AI tools
        search_terms = ["ai", "chatbot", "generator", "automation", "productivity"]
        
        for term in search_terms:
            if len(tools) >= max_tools:
                break
                
            try:
                # Try different search URL formats
                search_urls = [
                    f"https://toolify.ai/search?q={term}",
                    f"https://toolify.ai/search/{term}",
                    f"https://toolify.ai/?search={term}"
                ]
                
                for search_url in search_urls:
                    search_tools = self._scrape_toolify_url(search_url, max_tools - len(tools))
                    if search_tools:
                        tools.extend(search_tools)
                        break
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.warning(f"    ⚠️ Search term {term} failed: {str(e)}")
                continue
        
        return tools
    
    def _try_toolify_browse(self, max_tools: int) -> list:
        """Try Toolify browse/explore pages"""
        browse_urls = [
            "https://toolify.ai/browse",
            "https://toolify.ai/explore",
            "https://toolify.ai/tools",
            "https://toolify.ai/directory",
            "https://toolify.ai/popular",
            "https://toolify.ai/trending"
        ]
        
        tools = []
        for url in browse_urls:
            if len(tools) >= max_tools:
                break
                
            browse_tools = self._scrape_toolify_url(url, max_tools - len(tools))
            tools.extend(browse_tools)
            
            time.sleep(random.uniform(2, 4))
        
        return tools
    
    def _try_toolify_sitemap(self, max_tools: int) -> list:
        """Try to discover tools via sitemap"""
        tools = []
        
        try:
            sitemap_urls = [
                "https://toolify.ai/sitemap.xml",
                "https://toolify.ai/robots.txt"
            ]
            
            for sitemap_url in sitemap_urls:
                try:
                    response = requests.get(sitemap_url, timeout=10, headers={
                        'User-Agent': random.choice(self.user_agents)
                    })
                    
                    if response.status_code == 200:
                        # Extract tool URLs from sitemap
                        tool_urls = re.findall(r'https://toolify\.ai/[^<\s]+', response.text)
                        
                        for tool_url in tool_urls[:max_tools]:
                            if '/tool/' in tool_url or any(category in tool_url for category in ['ai-', 'chat', 'generator']):
                                # Extract tool name from URL
                                tool_name = tool_url.split('/')[-1].replace('-', ' ').title()
                                
                                tools.append({
                                    'tool_name_on_directory': tool_name,
                                    'external_website_url': tool_url,
                                    'description': '',
                                    'category': 'Toolify Discovery',
                                    'source': 'toolify_sitemap'
                                })
                        
                        if tools:
                            break
                            
                except Exception as e:
                    logger.warning(f"    ⚠️ Sitemap {sitemap_url} failed: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.warning(f"  ⚠️ Sitemap strategy failed: {str(e)}")
        
        return tools[:max_tools]
    
    def _scrape_toolify_url(self, url: str, max_tools: int) -> list:
        """Scrape a specific Toolify URL"""
        logger.info(f"    🌐 Scraping: {url}")
        tools = []
        
        try:
            # Enhanced headers to bypass Cloudflare
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            }
            
            # Add random delay to appear more human
            time.sleep(random.uniform(1, 3))
            
            response = requests.get(url, headers=headers, timeout=15)
            
            logger.info(f"    📊 Response: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                tools = self._extract_toolify_tools(soup, url)
                
                if tools:
                    logger.info(f"    ✅ Extracted {len(tools)} tools from {url}")
                else:
                    logger.info(f"    📝 No tools found in page content")
                    # Debug: show page structure
                    self._debug_page_structure(soup, url)
                    
            elif response.status_code == 403:
                logger.warning(f"    🛡️ Cloudflare protection detected for {url}")
                # Try alternative approach
                tools = self._try_cloudflare_bypass(url, headers)
                
            elif response.status_code == 404:
                logger.warning(f"    ❌ Page not found: {url}")
                
            else:
                logger.warning(f"    ⚠️ Unexpected status {response.status_code} for {url}")
                
        except requests.exceptions.Timeout:
            logger.warning(f"    ⏰ Timeout accessing {url}")
        except requests.exceptions.RequestException as e:
            logger.warning(f"    🌐 Network error for {url}: {str(e)}")
        except Exception as e:
            logger.warning(f"    ❌ Error scraping {url}: {str(e)}")
        
        return tools[:max_tools]
    
    def _extract_toolify_tools(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract tools from Toolify page HTML"""
        tools = []
        
        # Multiple selector strategies for Toolify
        tool_selectors = [
            # Common tool card selectors
            '.tool-card',
            '.ai-tool-card',
            '.tool-item',
            '.grid-item',
            '[data-tool]',
            '.product-card',
            
            # Link-based selectors
            'a[href*="/tool/"]',
            'a[href*="/ai-tools/"]',
            'a[href*="/tools/"]',
            
            # React/Vue component selectors
            '[class*="tool"]',
            '[class*="card"]',
            '[data-testid*="tool"]',
            
            # Generic content selectors
            '.list-item',
            '.item',
            'article'
        ]
        
        for selector in tool_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"      📦 Found {len(elements)} elements with selector: {selector}")
                
                for element in elements[:50]:  # Limit per selector
                    tool_data = self._extract_tool_from_element(element, base_url)
                    if tool_data:
                        tools.append(tool_data)
                
                if tools:
                    break  # Use first successful selector
        
        # If no specific selectors work, try text-based extraction
        if not tools:
            tools = self._extract_tools_by_patterns(soup, base_url)
        
        return tools
    
    def _extract_tool_from_element(self, element, base_url: str) -> dict:
        """Extract tool data from a single HTML element"""
        try:
            # Extract tool name
            name_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '.tool-name', 'a']
            name = ""
            
            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    name = name_elem.get_text().strip()
                    if len(name) > 3 and len(name) < 100:
                        break
            
            # Extract URL
            url = ""
            link_elem = element.find('a')
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    url = urljoin(base_url, href)
                elif href.startswith('http'):
                    url = href
            
            # Extract description
            desc_selectors = ['.description', '.summary', 'p', '.excerpt', '.content']
            description = ""
            
            for selector in desc_selectors:
                desc_elem = element.select_one(selector)
                if desc_elem:
                    description = desc_elem.get_text().strip()
                    if len(description) > 20:
                        break
            
            # Extract category/tags
            category_selectors = ['.category', '.tag', '.badge', '.label']
            category = ""
            
            for selector in category_selectors:
                cat_elem = element.select_one(selector)
                if cat_elem:
                    category = cat_elem.get_text().strip()
                    break
            
            # Validate and return
            if name and len(name) > 2:
                return {
                    'tool_name_on_directory': name,
                    'external_website_url': url or f"https://toolify.ai/tool/{quote(name.lower().replace(' ', '-'))}",
                    'description': description,
                    'category': category or 'AI Tools',
                    'source': 'toolify'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"      ⚠️ Error extracting from element: {str(e)}")
            return None
    
    def _extract_tools_by_patterns(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract tools using text patterns and AI-related keywords"""
        tools = []
        
        # Look for AI tool patterns in text
        ai_patterns = [
            r'([A-Z][a-zA-Z\s]{2,30}(?:AI|Tool|Generator|Assistant|Bot))',
            r'((?:AI|Smart|Auto|Intelligent)\s+[A-Z][a-zA-Z\s]{2,30})',
        ]
        
        page_text = soup.get_text()
        
        for pattern in ai_patterns:
            matches = re.findall(pattern, page_text)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                
                # Clean and validate
                clean_name = re.sub(r'\s+', ' ', match).strip()
                if 5 < len(clean_name) < 50:
                    tools.append({
                        'tool_name_on_directory': clean_name,
                        'external_website_url': f"https://toolify.ai/search?q={quote(clean_name)}",
                        'description': '',
                        'category': 'Pattern Discovery',
                        'source': 'toolify_pattern'
                    })
        
        # Also look for links with AI-related text
        links = soup.find_all('a', href=True)
        for link in links:
            text = link.get_text().strip()
            href = link.get('href', '')
            
            if (any(keyword in text.lower() for keyword in ['ai', 'tool', 'generator', 'assistant']) and
                len(text) > 5 and len(text) < 80):
                
                # Build full URL
                if href.startswith('/'):
                    full_url = urljoin(base_url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue
                
                tools.append({
                    'tool_name_on_directory': text,
                    'external_website_url': full_url,
                    'description': '',
                    'category': 'Link Discovery',
                    'source': 'toolify_link'
                })
        
        return tools[:20]  # Limit pattern-based results
    
    def _debug_page_structure(self, soup: BeautifulSoup, url: str):
        """Debug page structure to understand Toolify's layout"""
        logger.info(f"    🔍 Debug: Analyzing page structure for {url}")
        
        # Check for common elements
        elements_to_check = [
            ('titles', ['h1', 'h2', 'h3']),
            ('links', ['a[href]']),
            ('images', ['img']),
            ('divs with classes', ['div[class]']),
            ('buttons', ['button']),
        ]
        
        for element_type, selectors in elements_to_check:
            total_found = 0
            for selector in selectors:
                found = len(soup.select(selector))
                total_found += found
            
            logger.info(f"      📊 {element_type}: {total_found}")
        
        # Check for React/Vue indicators
        react_indicators = [
            'id="root"',
            'id="app"', 
            'data-reactroot',
            'ng-app',
            '__NEXT_DATA__'
        ]
        
        page_html = str(soup)
        for indicator in react_indicators:
            if indicator in page_html:
                logger.info(f"      ⚛️ Found SPA indicator: {indicator}")
        
        # Show first few titles found
        titles = soup.select('h1, h2, h3')[:5]
        if titles:
            logger.info(f"      📝 Sample titles: {[t.get_text().strip()[:50] for t in titles]}")
    
    def _try_cloudflare_bypass(self, url: str, headers: dict) -> list:
        """Try to bypass Cloudflare protection"""
        logger.info(f"    🛡️ Attempting Cloudflare bypass for {url}")
        
        # Enhanced headers for bypass
        bypass_headers = headers.copy()
        bypass_headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
        })
        
        # Try with different approaches
        approaches = [
            # Approach 1: Wait and retry
            lambda: self._wait_and_retry(url, bypass_headers),
            
            # Approach 2: Try mobile user agent
            lambda: self._try_mobile_agent(url),
            
            # Approach 3: Try different subdomain
            lambda: self._try_alternative_domains(url),
        ]
        
        for approach in approaches:
            try:
                tools = approach()
                if tools:
                    return tools
                time.sleep(2)
            except Exception as e:
                logger.warning(f"      ⚠️ Bypass approach failed: {str(e)}")
                continue
        
        return []
    
    def _wait_and_retry(self, url: str, headers: dict) -> list:
        """Wait and retry with different timing"""
        time.sleep(random.uniform(5, 10))
        
        response = requests.get(url, headers=headers, timeout=20)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            return self._extract_toolify_tools(soup, url)
        
        return []
    
    def _try_mobile_agent(self, url: str) -> list:
        """Try with mobile user agent"""
        mobile_headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        }
        
        response = requests.get(url, headers=mobile_headers, timeout=15)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            return self._extract_toolify_tools(soup, url)
        
        return []
    
    def _try_alternative_domains(self, url: str) -> list:
        """Try alternative domains or endpoints"""
        alternative_urls = []
        
        if 'toolify.ai' in url:
            # Try with www
            alternative_urls.append(url.replace('toolify.ai', 'www.toolify.ai'))
            # Try different paths
            base_domain = url.split('/')[2]
            alternative_urls.extend([
                f"https://{base_domain}/browse",
                f"https://{base_domain}/tools",
                f"https://{base_domain}/directory"
            ])
        
        for alt_url in alternative_urls:
            try:
                response = requests.get(alt_url, timeout=10, headers={
                    'User-Agent': random.choice(self.user_agents)
                })
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    tools = self._extract_toolify_tools(soup, alt_url)
                    if tools:
                        return tools
                        
            except Exception as e:
                continue
        
        return []
    
    def _deduplicate_tools(self, tools: list) -> list:
        """Remove duplicate tools"""
        seen = set()
        unique_tools = []
        
        for tool in tools:
            # Create a key for deduplication
            name = tool.get('tool_name_on_directory', '').lower().strip()
            url = tool.get('external_website_url', '').lower().strip()
            
            # Simple dedup key
            key = f"{name}|{urlparse(url).netloc if url else ''}"
            
            if key not in seen and name and len(name) > 2:
                seen.add(key)
                unique_tools.append(tool)
        
        return unique_tools
    
    def process_toolify_tools(self, tools: list) -> dict:
        """Process discovered Toolify tools"""
        logger.info(f"🚀 Processing {len(tools)} Toolify tools...")
        
        success_count = 0
        failed_count = 0
        
        for i, tool in enumerate(tools):
            logger.info(f"\n📦 Processing tool {i+1}/{len(tools)}: {tool.get('tool_name_on_directory', 'Unknown')}")
            
            try:
                # Use the same processing logic as multi-source processor
                # For now, just validate the tool structure
                if self._validate_tool_data(tool):
                    logger.info(f"  ✅ Tool validated: {tool['tool_name_on_directory']}")
                    success_count += 1
                else:
                    logger.warning(f"  ❌ Tool validation failed: {tool.get('tool_name_on_directory', 'Unknown')}")
                    failed_count += 1
                    
                # Small delay to be respectful
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"  ❌ Error processing tool: {str(e)}")
                failed_count += 1
        
        return {
            'total_tools': len(tools),
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': (success_count / len(tools) * 100) if tools else 0
        }
    
    def _validate_tool_data(self, tool: dict) -> bool:
        """Validate tool data structure"""
        required_fields = ['tool_name_on_directory', 'external_website_url']
        
        for field in required_fields:
            if not tool.get(field):
                return False
        
        # Validate name length
        name = tool.get('tool_name_on_directory', '')
        if len(name) < 3 or len(name) > 100:
            return False
        
        # Validate URL format
        url = tool.get('external_website_url', '')
        if not (url.startswith('http') or url.startswith('/')):
            return False
        
        return True

def main():
    """Test Toolify scraping with 50 tools"""
    
    XAI_API_KEY = "************************************************************************************"
    PERPLEXITY_API_KEY = "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"
    
    scraper = ToolifyScraperTester(XAI_API_KEY, PERPLEXITY_API_KEY)
    
    logger.info("🎯 TOOLIFY SCRAPING TEST - TARGET: 50 TOOLS")
    logger.info("="*60)
    
    # Test Toolify scraping
    toolify_tools = scraper.test_toolify_scraping(max_tools=50)
    
    if toolify_tools:
        logger.info(f"✅ TOOLIFY SUCCESS: Found {len(toolify_tools)} tools")
        
        # Show sample tools
        logger.info("\n📋 SAMPLE DISCOVERED TOOLS:")
        for i, tool in enumerate(toolify_tools[:10]):
            logger.info(f"  {i+1}. {tool.get('tool_name_on_directory', 'Unknown')} ({tool.get('source', 'unknown')})")
        
        # Process tools (validation only for now)
        results = scraper.process_toolify_tools(toolify_tools)
        
        logger.info(f"\n📊 PROCESSING RESULTS:")
        logger.info(f"  Total Tools: {results['total_tools']}")
        logger.info(f"  Success: {results['success_count']}")
        logger.info(f"  Failed: {results['failed_count']}")
        logger.info(f"  Success Rate: {results['success_rate']:.1f}%")
        
        if results['success_rate'] >= 70:
            logger.info("🎉 TOOLIFY IS WORKING WELL - READY FOR PRODUCTION!")
        else:
            logger.warning("⚠️ TOOLIFY NEEDS OPTIMIZATION")
    else:
        logger.error("❌ TOOLIFY SCRAPING FAILED - NO TOOLS FOUND")

if __name__ == "__main__":
    main()