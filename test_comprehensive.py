"""
Simple Test Script for Comprehensive Multi-Source Processing
This demonstrates the full pipeline with cost-optimized AI enhancement
"""

import sys
sys.path.append('/app')

import json
import logging
from comprehensive_processor import ComprehensiveProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_single_tool():
    """Test the comprehensive processor with a single tool"""
    
    # Use provided XAI API key
    XAI_API_KEY = "************************************************************************************"
    PERPLEXITY_API_KEY = "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"
    
    print("💰 COST-OPTIMIZED PROCESSING:")
    print("  • XAI (Free Credits): Heavy lifting & content analysis")
    print("  • Perplexity (Cheapest): Only factual gap-filling")
    print("  • Estimated cost per tool: ~$0.02-0.05")
    print()
    
    # Initialize processor
    processor = ComprehensiveProcessor(
        xai_api_key=XAI_API_KEY,
        perplexity_api_key=PERPLEXITY_API_KEY
    )
    
    # Test with a tool that hasn't been processed yet
    test_tool = {
        'tool_name_on_directory': 'MatMat AI',
        'external_website_url': 'https://futuretools.link/matmat-ai'
    }
    
    print("🧪 TESTING COMPREHENSIVE MULTI-SOURCE PROCESSOR")
    print("="*60)
    print(f"Tool: {test_tool['tool_name_on_directory']}")
    print(f"URL: {test_tool['external_website_url']}")
    print()
    
    # Process the tool
    result = processor.process_and_enhance_entity(test_tool)
    
    if result:
        print("✅ SUCCESS! Tool processed with comprehensive enhancement")
        print(f"Entity ID: {result}")
    else:
        print("❌ FAILED to process tool")

def show_expected_quality():
    """Show what quality we should expect"""
    print("🌟 EXPECTED QUALITY WITH COMPREHENSIVE PROCESSOR:")
    print("="*60)
    
    expected_schema = {
        "name": "MatMat AI",
        "website_url": "https://www.matmat.ai/",  # ✅ ACTUAL URL (not FutureTools)
        "short_description": "AI-powered customer conversation management platform that unifies all communication channels into a single intelligent inbox for businesses.",  # ✅ RICH, SPECIFIC
        "description": "MatMat AI consolidates customer communications from email, SMS, social media, and chat into one unified interface. The platform uses AI to automatically categorize, prioritize, and route conversations while providing intelligent response suggestions and automated follow-ups to help businesses never miss important customer interactions.",  # ✅ COMPREHENSIVE
        "key_features": [  # ✅ EXTRACTED FROM ACTUAL CONTENT
            "Unified Multi-Channel Inbox",
            "AI-Powered Message Categorization", 
            "Intelligent Response Suggestions",
            "Automated Follow-up Sequences",
            "Real-time Conversation Analytics",
            "Team Collaboration Tools",
            "Integration with Popular CRM Systems",
            "Mobile App for On-the-Go Management"
        ],
        "use_cases": [  # ✅ SPECIFIC TO THE TOOL
            "Customer Support Management",
            "Sales Lead Nurturing", 
            "Multi-Channel Customer Communication",
            "Team Collaboration and Handoffs",
            "Customer Retention and Engagement",
            "Response Time Optimization"
        ],
        "target_audience": [  # ✅ RESEARCHED
            "Customer Support Teams",
            "Sales Teams", 
            "Small to Medium Businesses",
            "E-commerce Companies"
        ],
        "social_links": {  # ✅ CLEAN HANDLES
            "twitter": "matmatai",
            "linkedin": "matmat-ai",
            "github": "matmat-ai"
        },
        "additional_urls": {  # ✅ DISCOVERED URLs
            "blog_url": "https://www.matmat.ai/blog",
            "documentation_url": "https://docs.matmat.ai",
            "support_url": "https://support.matmat.ai"
        },
        "founded_year": 2023,  # ✅ RESEARCHED
        "employee_count_range": "C11_50",  # ✅ RESEARCHED
        "funding_stage": "SEED",  # ✅ RESEARCHED
        "location_summary": "San Francisco, CA, USA",  # ✅ RESEARCHED
        "logo_url": "https://www.matmat.ai/logo.svg",  # ✅ EXTRACTED
        "ai_tool_details": {
            "integrations": ["Slack", "Salesforce", "HubSpot"],  # ✅ RESEARCHED
            "programming_languages": ["Python", "JavaScript"],  # ✅ TECH STACK
            "deployment_options": ["Cloud", "SaaS"],  # ✅ DEPLOYMENT
            "security_features": ["SOC2", "GDPR"],  # ✅ COMPLIANCE
            "has_live_chat": True,  # ✅ DETECTED
            "api_access": True,  # ✅ RESEARCHED
            "pricing_model": "SUBSCRIPTION",  # ✅ RESEARCHED
            "review_sentiment_data": {  # ✅ SENTIMENT ANALYSIS
                "avg_rating": 4.3,
                "review_count": 47,
                "sentiment_label": "POSITIVE"
            }
        }
    }
    
    print(json.dumps(expected_schema, indent=2))
    print()
    print("🎯 KEY IMPROVEMENTS:")
    print("✅ ACTUAL website URLs (not FutureTools redirects)")
    print("✅ RICH, specific descriptions (not generic)")
    print("✅ EXTRACTED features from actual content") 
    print("✅ RESEARCHED company data (funding, location, etc.)")
    print("✅ CLEAN social media handles")
    print("✅ COMPREHENSIVE schema completion")
    print("✅ COST-OPTIMIZED: XAI for basics, Perplexity for research")

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE AI NAVIGATOR PROCESSOR")
    print("="*60)
    print()
    print("This system provides:")
    print("• Multi-source data collection (website + external sources)")
    print("• Cost-optimized AI enhancement (XAI + Perplexity)")
    print("• Entity deduplication and updating")
    print("• Complete schema maximization")
    print("• Real URL resolution and comprehensive scraping")
    print()
    
    choice = input("Choose option:\n1. Show expected quality\n2. Test with single tool\nChoice (1 or 2): ").strip()
    
    if choice == "1":
        show_expected_quality()
    elif choice == "2":
        test_single_tool()
    else:
        print("Invalid choice")