"""
Test TheresAnAIForThat and Product Hunt Scraping
Since Toolify requires browser automation, test confirmed working sources
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, quote
import re
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
import time
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkingSourcesTester:
    def __init__(self, xai_api_key: str, perplexity_api_key: str):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.xai_api_key = xai_api_key
        self.perplexity_api_key = perplexity_api_key
        
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    def test_theresanaiforthat(self, max_tools: int = 50) -> list:
        """Test TheresAnAIForThat.com scraping"""
        logger.info(f"🔍 Testing TheresAnAIForThat.com for {max_tools} tools...")
        
        tools = []
        
        # Try multiple TAAFT endpoints
        taaft_urls = [
            "https://theresanaiforthat.com/",
            "https://theresanaiforthat.com/trending/",
            "https://theresanaiforthat.com/newest/",
            "https://theresanaiforthat.com/most-saved/",
            "https://theresanaiforthat.com/categories/",
            "https://theresanaiforthat.com/ai-tools/"
        ]
        
        for url in taaft_urls:
            if len(tools) >= max_tools:
                break
                
            try:
                logger.info(f"  🌐 Testing TAAFT: {url}")
                
                headers = {
                    'User-Agent': random.choice(self.user_agents),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
                
                response = requests.get(url, headers=headers, timeout=15)
                
                logger.info(f"    📊 TAAFT Response: {response.status_code}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_tools = self._extract_taaft_tools(soup, url)
                    
                    if page_tools:
                        tools.extend(page_tools)
                        logger.info(f"    ✅ Found {len(page_tools)} tools from TAAFT")
                    else:
                        logger.info(f"    📝 No tools found on TAAFT page")
                        self._debug_taaft_structure(soup, url)
                        
                elif response.status_code == 403:
                    logger.warning(f"    🛡️ TAAFT Cloudflare protection detected")
                    # Try mobile approach
                    mobile_tools = self._try_taaft_mobile(url)
                    tools.extend(mobile_tools)
                    
                else:
                    logger.warning(f"    ⚠️ TAAFT unexpected status: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.warning(f"    ❌ TAAFT error for {url}: {str(e)}")
                continue
        
        logger.info(f"🎉 TAAFT Test Complete: {len(tools)} tools found")
        return tools[:max_tools]
    
    def _extract_taaft_tools(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract tools from TAAFT page"""
        tools = []
        
        # TAAFT-specific selectors
        taaft_selectors = [
            '.tool-card',
            '.ai-tool',
            '.tool-item',
            '[data-tool]',
            '.grid-item',
            'a[href*="/ai/"]',
            'a[href*="/tool/"]',
            '.tool-grid a',
            '.tools-list a'
        ]
        
        for selector in taaft_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"      📦 TAAFT found {len(elements)} elements with: {selector}")
                
                for element in elements[:30]:
                    tool_data = self._extract_taaft_tool_data(element, base_url)
                    if tool_data:
                        tools.append(tool_data)
                
                if tools:
                    break
        
        # Fallback: look for AI-related links
        if not tools:
            tools = self._extract_taaft_by_links(soup, base_url)
        
        return tools
    
    def _extract_taaft_tool_data(self, element, base_url: str) -> dict:
        """Extract tool data from TAAFT element"""
        try:
            # Extract name
            name_selectors = ['h2', 'h3', '.title', '.tool-name', 'a']
            name = ""
            
            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    name = name_elem.get_text().strip()
                    if 3 < len(name) < 80:
                        break
            
            # Extract URL
            url = ""
            link = element.find('a') if element.name != 'a' else element
            if link and link.get('href'):
                href = link['href']
                if href.startswith('/'):
                    url = urljoin(base_url, href)
                elif href.startswith('http'):
                    url = href
            
            # Extract description
            desc_selectors = ['.description', '.summary', 'p']
            description = ""
            
            for selector in desc_selectors:
                desc_elem = element.select_one(selector)
                if desc_elem:
                    description = desc_elem.get_text().strip()
                    if len(description) > 20:
                        break
            
            if name and len(name) > 3:
                return {
                    'tool_name_on_directory': name,
                    'external_website_url': url or f"https://theresanaiforthat.com/search/?q={quote(name)}",
                    'description': description,
                    'category': 'TAAFT Discovery',
                    'source': 'theresanaiforthat'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"      ⚠️ TAAFT extraction error: {str(e)}")
            return None
    
    def _extract_taaft_by_links(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract TAAFT tools by analyzing links"""
        tools = []
        
        links = soup.find_all('a', href=True)
        for link in links:
            text = link.get_text().strip()
            href = link.get('href', '')
            
            # Look for AI tool patterns
            if (len(text) > 5 and len(text) < 60 and
                any(keyword in text.lower() for keyword in ['ai', 'tool', 'generator', 'assistant']) and
                ('/ai/' in href or '/tool/' in href or 'ai' in text.lower())):
                
                if href.startswith('/'):
                    full_url = urljoin(base_url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue
                
                tools.append({
                    'tool_name_on_directory': text,
                    'external_website_url': full_url,
                    'description': '',
                    'category': 'TAAFT Link Discovery',
                    'source': 'theresanaiforthat_link'
                })
        
        return tools[:15]
    
    def _debug_taaft_structure(self, soup: BeautifulSoup, url: str):
        """Debug TAAFT page structure"""
        logger.info(f"    🔍 Debug: TAAFT page structure for {url}")
        
        # Check common elements
        titles = len(soup.select('h1, h2, h3'))
        links = len(soup.select('a[href]'))
        images = len(soup.select('img'))
        divs = len(soup.select('div[class]'))
        
        logger.info(f"      📊 TAAFT titles: {titles}, links: {links}, images: {images}, divs: {divs}")
        
        # Check for specific TAAFT indicators
        taaft_indicators = ['there', 'ai', 'tool', 'artificial intelligence']
        page_text = soup.get_text().lower()
        
        found_indicators = [ind for ind in taaft_indicators if ind in page_text]
        logger.info(f"      🎯 TAAFT indicators found: {found_indicators}")
    
    def _try_taaft_mobile(self, url: str) -> list:
        """Try TAAFT with mobile user agent"""
        try:
            mobile_headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            }
            
            response = requests.get(url, headers=mobile_headers, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                return self._extract_taaft_tools(soup, url)
        except:
            pass
        
        return []
    
    def test_product_hunt_ai(self, max_tools: int = 50) -> list:
        """Test Product Hunt AI tools scraping"""
        logger.info(f"🦄 Testing Product Hunt AI for {max_tools} tools...")
        
        tools = []
        
        # Product Hunt AI-focused URLs
        ph_urls = [
            "https://www.producthunt.com/topics/artificial-intelligence",
            "https://www.producthunt.com/search?q=AI+tool",
            "https://www.producthunt.com/search?q=artificial+intelligence",
            "https://www.producthunt.com/search?q=machine+learning",
            "https://www.producthunt.com/search?q=chatbot",
            "https://www.producthunt.com/search?q=generator",
            "https://www.producthunt.com/ai-tools",
            "https://www.producthunt.com/collections/ai-tools"
        ]
        
        for url in ph_urls:
            if len(tools) >= max_tools:
                break
                
            try:
                logger.info(f"  🌐 Testing Product Hunt: {url}")
                
                headers = {
                    'User-Agent': random.choice(self.user_agents),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                }
                
                response = requests.get(url, headers=headers, timeout=15)
                
                logger.info(f"    📊 Product Hunt Response: {response.status_code}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_tools = self._extract_ph_tools(soup, url)
                    
                    if page_tools:
                        tools.extend(page_tools)
                        logger.info(f"    ✅ Found {len(page_tools)} tools from Product Hunt")
                    else:
                        logger.info(f"    📝 No tools found on Product Hunt page")
                        self._debug_ph_structure(soup, url)
                        
                else:
                    logger.warning(f"    ⚠️ Product Hunt unexpected status: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.warning(f"    ❌ Product Hunt error for {url}: {str(e)}")
                continue
        
        logger.info(f"🎉 Product Hunt Test Complete: {len(tools)} tools found")
        return tools[:max_tools]
    
    def _extract_ph_tools(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract tools from Product Hunt page"""
        tools = []
        
        # Product Hunt selectors
        ph_selectors = [
            'a[href*="/posts/"]',
            '[data-test*="post"] a',
            '.product-item a',
            'h3 a',
            'h2 a',
            '.post-link',
            '[class*="post"] a',
            '[class*="product"] a'
        ]
        
        for selector in ph_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"      📦 Product Hunt found {len(elements)} elements with: {selector}")
                
                for element in elements[:25]:
                    tool_data = self._extract_ph_tool_data(element, base_url)
                    if tool_data:
                        tools.append(tool_data)
                
                if tools:
                    break
        
        return tools
    
    def _extract_ph_tool_data(self, element, base_url: str) -> dict:
        """Extract tool data from Product Hunt element"""
        try:
            # Extract name
            name = element.get_text().strip()
            if len(name) < 3 or len(name) > 100:
                return None
            
            # Extract Product Hunt URL
            href = element.get('href', '')
            if href.startswith('/'):
                ph_url = f"https://www.producthunt.com{href}"
            else:
                ph_url = href
            
            # Try to resolve to actual website
            actual_url = self._resolve_ph_to_actual_website(ph_url, name)
            
            return {
                'tool_name_on_directory': name,
                'external_website_url': actual_url,
                'description': '',
                'category': 'Product Hunt AI',
                'source': 'product_hunt'
            }
            
        except Exception as e:
            logger.warning(f"      ⚠️ Product Hunt extraction error: {str(e)}")
            return None
    
    def _resolve_ph_to_actual_website(self, ph_url: str, tool_name: str) -> str:
        """Resolve Product Hunt URL to actual website"""
        # For testing, return the Product Hunt URL
        # In production, we could try to scrape the PH page for the actual website
        return ph_url
    
    def _debug_ph_structure(self, soup: BeautifulSoup, url: str):
        """Debug Product Hunt page structure"""
        logger.info(f"    🔍 Debug: Product Hunt page structure for {url}")
        
        titles = len(soup.select('h1, h2, h3'))
        links = len(soup.select('a[href]'))
        posts = len(soup.select('[href*="/posts/"]'))
        
        logger.info(f"      📊 Product Hunt titles: {titles}, links: {links}, posts: {posts}")
    
    def process_discovered_tools(self, tools: list, source_name: str) -> dict:
        """Process discovered tools and create entities"""
        logger.info(f"🚀 Processing {len(tools)} {source_name} tools...")
        
        from working_multi_source import WorkingMultiSourceProcessor
        processor = WorkingMultiSourceProcessor(self.xai_api_key, self.perplexity_api_key)
        
        success_count = 0
        for i, tool in enumerate(tools[:10]):  # Process first 10 for testing
            logger.info(f"\n📦 Processing {source_name} tool {i+1}/10: {tool.get('tool_name_on_directory', 'Unknown')}")
            
            try:
                success = processor.process_tool(tool)
                if success:
                    success_count += 1
                    
                time.sleep(2)  # Be respectful
                
            except Exception as e:
                logger.error(f"  ❌ Error processing {source_name} tool: {str(e)}")
        
        return {
            'source': source_name,
            'total_discovered': len(tools),
            'processed': 10,
            'successful': success_count,
            'success_rate': (success_count / 10 * 100) if tools else 0
        }

def main():
    """Test working sources: TAAFT and Product Hunt"""
    
    XAI_API_KEY = "************************************************************************************"
    PERPLEXITY_API_KEY = "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"
    
    tester = WorkingSourcesTester(XAI_API_KEY, PERPLEXITY_API_KEY)
    
    logger.info("🎯 TESTING CONFIRMED WORKING SOURCES")
    logger.info("="*60)
    
    results = {}
    
    # Test 1: TheresAnAIForThat
    logger.info("\n📋 TEST 1: TheresAnAIForThat.com")
    taaft_tools = tester.test_theresanaiforthat(max_tools=25)
    
    if taaft_tools:
        logger.info(f"✅ TAAFT SUCCESS: {len(taaft_tools)} tools discovered")
        results['taaft'] = tester.process_discovered_tools(taaft_tools, "TAAFT")
    else:
        logger.warning("❌ TAAFT FAILED: No tools found")
        results['taaft'] = {'source': 'TAAFT', 'status': 'failed'}
    
    # Test 2: Product Hunt
    logger.info("\n📋 TEST 2: Product Hunt AI")
    ph_tools = tester.test_product_hunt_ai(max_tools=25)
    
    if ph_tools:
        logger.info(f"✅ PRODUCT HUNT SUCCESS: {len(ph_tools)} tools discovered")
        results['product_hunt'] = tester.process_discovered_tools(ph_tools, "Product Hunt")
    else:
        logger.warning("❌ PRODUCT HUNT FAILED: No tools found")
        results['product_hunt'] = {'source': 'Product Hunt', 'status': 'failed'}
    
    # Summary
    logger.info("\n🎉 FINAL RESULTS SUMMARY")
    logger.info("="*60)
    
    for source, result in results.items():
        if 'status' in result and result['status'] == 'failed':
            logger.info(f"{source.upper()}: ❌ FAILED")
        else:
            logger.info(f"{source.upper()}: ✅ {result['successful']}/{result['processed']} processed ({result['success_rate']:.1f}%)")
    
    # Determine which sources are working
    working_sources = []
    for source, result in results.items():
        if 'success_rate' in result and result['success_rate'] >= 50:
            working_sources.append(source.upper())
    
    if working_sources:
        logger.info(f"\n🎯 CONFIRMED WORKING SOURCES: {', '.join(working_sources)}")
        logger.info("Ready for production scaling!")
    else:
        logger.warning("\n⚠️ NO SOURCES CONFIRMED WORKING - NEED ALTERNATIVE APPROACH")

if __name__ == "__main__":
    main()