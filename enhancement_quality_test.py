"""
Enhancement Quality Test
Tests our enhancement workflow with sample tools to measure data quality
"""

import sys
sys.path.append('/app')

import json
import logging
from typing import Dict, List, Any
from datetime import datetime
from config import config
from data_enrichment_service import DataEnrichmentService
from enhanced_taxonomy_service import EnhancedTaxonomyService
from ai_navigator_client import AINavigatorClient
from entity_type_detector import EntityTypeDetector
from shared.logging_config import setup_logging

class EnhancementQualityTester:
    """Test enhancement workflow quality with sample tools"""

    def __init__(self):
        self.logger = setup_logging(__name__)

        # Initialize services
        self.client = AINavigatorClient()
        self.enrichment_service = DataEnrichmentService(config.api.perplexity_api_key)
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()

        # Test sample tools
        self.sample_tools = [
            {
                "name": "ChatGPT",
                "url": "https://chat.openai.com",
                "expected_type": "ai-tool"
            },
            {
                "name": "GitHub Copilot",
                "url": "https://github.com/features/copilot",
                "expected_type": "ai-tool"
            },
            {
                "name": "Midjourney",
                "url": "https://midjourney.com",
                "expected_type": "ai-tool"
            },
            {
                "name": "Hugging Face",
                "url": "https://huggingface.co",
                "expected_type": "ai-tool"
            },
            {
                "name": "Stable Diffusion",
                "url": "https://stability.ai",
                "expected_type": "ai-tool"
            },
            {
                "name": "Claude",
                "url": "https://claude.ai",
                "expected_type": "ai-tool"
            },
            {
                "name": "Runway ML",
                "url": "https://runwayml.com",
                "expected_type": "ai-tool"
            },
            {
                "name": "Replicate",
                "url": "https://replicate.com",
                "expected_type": "ai-tool"
            },
            {
                "name": "Anthropic",
                "url": "https://anthropic.com",
                "expected_type": "ai-tool"
            },
            {
                "name": "OpenAI API",
                "url": "https://openai.com/api",
                "expected_type": "ai-tool"
            }
        ]

        # Quality metrics
        self.quality_metrics = {
            "total_tools": 0,
            "successful_enhancements": 0,
            "failed_enhancements": 0,
            "field_completeness": {},
            "enum_accuracy": {},
            "data_quality_scores": [],
            "processing_times": []
        }

    def evaluate_enhancement_quality(self, enhanced_data: Dict[str, Any], tool: Dict[str, str]) -> float:
        """Evaluate the quality of enhanced data"""

        if not enhanced_data or not enhanced_data.get('enriched_data'):
            return 0.0

        data = enhanced_data['enriched_data']
        quality_score = 0.0
        max_score = 100.0

        # Required fields (40 points)
        required_fields = ['name', 'short_description', 'description', 'key_features', 'use_cases']
        for field in required_fields:
            if data.get(field) and str(data[field]).strip() and str(data[field]) != 'unknown':
                quality_score += 8.0

        # Pricing information (20 points)
        pricing_fields = ['pricing_model', 'price_range', 'has_free_tier']
        for field in pricing_fields:
            if data.get(field) and str(data[field]) != 'unknown':
                quality_score += 6.67

        # Technical details (20 points)
        tech_fields = ['api_access', 'mobile_support', 'open_source', 'learning_curve']
        for field in tech_fields:
            if data.get(field) is not None and str(data[field]) != 'unknown':
                quality_score += 5.0

        # Taxonomy mapping (10 points)
        if enhanced_data.get('categories_mapped', 0) > 0:
            quality_score += 5.0
        if enhanced_data.get('tags_mapped', 0) > 0:
            quality_score += 5.0

        # Data richness (10 points)
        array_fields = ['integrations', 'frameworks', 'target_audience', 'support_channels']
        for field in array_fields:
            if data.get(field) and isinstance(data[field], list) and len(data[field]) > 0:
                quality_score += 2.5

        return min(quality_score, max_score)

    def generate_quality_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive quality report"""

        # Calculate overall metrics
        success_rate = (self.quality_metrics["successful_enhancements"] /
                       self.quality_metrics["total_tools"]) * 100

        avg_quality = (sum(self.quality_metrics["data_quality_scores"]) /
                      len(self.quality_metrics["data_quality_scores"])) if self.quality_metrics["data_quality_scores"] else 0

        avg_processing_time = (sum(self.quality_metrics["processing_times"]) /
                              len(self.quality_metrics["processing_times"])) if self.quality_metrics["processing_times"] else 0

        # Analyze field completeness
        field_analysis = self.analyze_field_completeness(results)

        # Generate recommendations
        recommendations = self.generate_recommendations(results, field_analysis)

        report = {
            "test_summary": {
                "total_tools_tested": self.quality_metrics["total_tools"],
                "successful_enhancements": self.quality_metrics["successful_enhancements"],
                "failed_enhancements": self.quality_metrics["failed_enhancements"],
                "success_rate_percent": round(success_rate, 2),
                "average_quality_score": round(avg_quality, 2),
                "average_processing_time_seconds": round(avg_processing_time, 2)
            },
            "field_completeness_analysis": field_analysis,
            "individual_results": results,
            "recommendations": recommendations,
            "generated_at": datetime.now().isoformat()
        }

        return report

    def analyze_field_completeness(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze field completeness across all results"""

        field_stats = {}
        successful_results = [r for r in results if r['success']]

        if not successful_results:
            return field_stats

        # Analyze key fields
        key_fields = [
            'short_description', 'description', 'key_features', 'use_cases',
            'pricing_model', 'price_range', 'has_free_tier', 'api_access',
            'mobile_support', 'open_source', 'learning_curve', 'integrations',
            'frameworks', 'target_audience', 'support_channels'
        ]

        for field in key_fields:
            present_count = 0
            valid_count = 0

            for result in successful_results:
                data = result['enhanced_data']['enriched_data']
                if field in data:
                    present_count += 1
                    if data[field] and str(data[field]) != 'unknown' and str(data[field]).strip():
                        valid_count += 1

            field_stats[field] = {
                "present_count": present_count,
                "valid_count": valid_count,
                "present_percentage": round((present_count / len(successful_results)) * 100, 2),
                "valid_percentage": round((valid_count / len(successful_results)) * 100, 2)
            }

        return field_stats

    def generate_recommendations(self, results: List[Dict[str, Any]], field_analysis: Dict[str, Any]) -> List[str]:
        """Generate improvement recommendations based on test results"""

        recommendations = []

        # Success rate recommendations
        success_rate = (self.quality_metrics["successful_enhancements"] /
                       self.quality_metrics["total_tools"]) * 100

        if success_rate < 90:
            recommendations.append(f"🔴 CRITICAL: Enhancement success rate is {success_rate:.1f}%. Improve error handling and API reliability.")

        # Quality score recommendations
        avg_quality = (sum(self.quality_metrics["data_quality_scores"]) /
                      len(self.quality_metrics["data_quality_scores"])) if self.quality_metrics["data_quality_scores"] else 0

        if avg_quality < 70:
            recommendations.append(f"🟡 MEDIUM: Average quality score is {avg_quality:.1f}%. Enhance data extraction prompts and validation.")

        # Field completeness recommendations
        for field, stats in field_analysis.items():
            if stats['valid_percentage'] < 50:
                recommendations.append(f"🔴 HIGH: Field '{field}' has only {stats['valid_percentage']:.1f}% valid data. Improve extraction for this field.")
            elif stats['valid_percentage'] < 80:
                recommendations.append(f"🟡 MEDIUM: Field '{field}' has {stats['valid_percentage']:.1f}% valid data. Consider enhancement.")

        # Processing time recommendations
        avg_time = (sum(self.quality_metrics["processing_times"]) /
                   len(self.quality_metrics["processing_times"])) if self.quality_metrics["processing_times"] else 0

        if avg_time > 30:
            recommendations.append(f"🟡 MEDIUM: Average processing time is {avg_time:.1f}s. Consider optimization for better performance.")

        return recommendations

def main():
    """Run the enhancement quality test"""
    tester = EnhancementQualityTester()

    try:
        report = tester.run_quality_test()

        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"enhancement_quality_report_{timestamp}.json"

        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        print(f"\n📊 Enhancement Quality Test Complete!")
        print(f"📄 Report saved to: {report_file}")
        print(f"\n🎯 Test Summary:")
        print(f"   Success Rate: {report['test_summary']['success_rate_percent']:.1f}%")
        print(f"   Average Quality: {report['test_summary']['average_quality_score']:.1f}/100")
        print(f"   Average Time: {report['test_summary']['average_processing_time_seconds']:.1f}s")

        print(f"\n💡 Top Recommendations:")
        for i, rec in enumerate(report['recommendations'][:5], 1):
            print(f"   {i}. {rec}")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())