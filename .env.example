# AI Navigator Scrapers Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# ENVIRONMENT
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# API KEYS (REQUIRED)
# =============================================================================
# XAI API Key for AI processing
XAI_API_KEY=your_xai_api_key_here

# Perplexity API Key for data enrichment
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# =============================================================================
# AI NAVIGATOR BACKEND (REQUIRED)
# =============================================================================
# Base URL for the AI Navigator API
AI_NAVIGATOR_BASE_URL=https://ai-nav.onrender.com

# Admin credentials for AI Navigator API
AI_NAVIGATOR_ADMIN_EMAIL=<EMAIL>
AI_NAVIGATOR_ADMIN_PASSWORD=your_secure_password

# =============================================================================
# DATABASE
# =============================================================================
# Database connection URL (if using direct database access)
DATABASE_URL=postgresql://user:password@host:port/database
DATABASE_TIMEOUT=30

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Server host and port
SERVER_HOST=0.0.0.0
SERVER_PORT=8001

# CORS origins (comma-separated for multiple origins)
# For development: http://localhost:3000
# For production: https://yourdomain.com,https://www.yourdomain.com
CORS_ORIGINS=http://localhost:3000

# =============================================================================
# SCRAPING CONFIGURATION
# =============================================================================
# User agent for web scraping
SCRAPING_USER_AGENT=AI Navigator Bot 1.0

# Delay between requests (in seconds)
SCRAPING_MIN_DELAY=1.0
SCRAPING_MAX_DELAY=3.0

# Number of concurrent requests
SCRAPING_CONCURRENT_REQUESTS=8

# Request timeout (in seconds)
SCRAPING_TIMEOUT=30

# Respect robots.txt
SCRAPING_RESPECT_ROBOTS=true

# =============================================================================
# FILE PATHS
# =============================================================================
# Directory containing Scrapy project
SCRAPY_DIR=/app/ai-navigator-scrapers

# Output directory for scraped data
OUTPUT_DIR=/app/ai-navigator-scrapers

# Log directory
LOG_DIR=/app/logs