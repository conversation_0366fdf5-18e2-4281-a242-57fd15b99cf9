# Environment variables and secrets
.env
.env.local
.env.production
.env.staging

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/
*.log.*

# Data files
*.jsonl
*.json
*.csv
*.xlsx
*.db
*.sqlite
*.sqlite3

# Scrapy
.scrapy/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# React build
frontend/build/
frontend/dist/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Docker
.dockerignore
Dockerfile.prod

# Backup files
*.bak
*.backup
*.old

# API keys and credentials (extra safety)
*api_key*
*secret*
*password*
*credential*

# Output files
output/
results/
data/
scraped_data/

# Error files
error_*.txt
failed_*.txt