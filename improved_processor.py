"""
Improved Tool Processor - Back to Quality Standards
Fix the regression and get back to world-class data quality
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import re
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedProcessor:
    def __init__(self):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        
    def get_entity_type_id(self, entity_type_slug):
        """Get entity type ID dynamically from API"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entity-types",
                headers=self.client._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entity_types = response.json()
                for et in entity_types:
                    if et.get('slug') == entity_type_slug:
                        return et.get('id')
            
            return None
                        
        except Exception as e:
            logger.error(f"Error getting entity type ID: {str(e)}")
            return None

    def resolve_to_actual_website(self, futuretools_url):
        """Properly resolve FutureTools redirect to actual website"""
        try:
            logger.info(f"  🔍 Resolving redirect: {futuretools_url}")
            
            # First, get the FutureTools page
            response = requests.get(futuretools_url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                # Check for meta refresh redirect (most common)
                meta_refresh = re.search(r'<meta[^>]*http-equiv=["\']refresh["\'][^>]*content=["\'][^>]*url=([^"\'>\s]+)', response.text, re.IGNORECASE)
                if meta_refresh:
                    url = meta_refresh.group(1)
                    # Clean the URL
                    if url.startswith('//'):
                        url = 'https:' + url
                    # Remove tracking parameters
                    url = re.sub(r'[\?&]ref=[^&]*', '', url)
                    url = re.sub(r'[\?&]utm_[^&]*=[^&]*', '', url)
                    logger.info(f"  ✅ Found via meta refresh: {url}")
                    return url
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Look for meta refresh in parsed HTML
                meta_refresh_tag = soup.find('meta', attrs={'http-equiv': re.compile(r'refresh', re.I)})
                if meta_refresh_tag:
                    content = meta_refresh_tag.get('content', '')
                    url_match = re.search(r'url=([^;\s]+)', content, re.IGNORECASE)
                    if url_match:
                        url = url_match.group(1)
                        if url.startswith('//'):
                            url = 'https:' + url
                        # Remove tracking parameters
                        url = re.sub(r'[\?&]ref=[^&]*', '', url)
                        url = re.sub(r'[\?&]utm_[^&]*=[^&]*', '', url)
                        logger.info(f"  ✅ Found via parsed meta refresh: {url}")
                        return url
                
                # Look for JavaScript redirects
                js_redirect = re.search(r'window\.location\.href\s*=\s*["\']([^"\']+)["\']', response.text, re.IGNORECASE)
                if js_redirect:
                    url = js_redirect.group(1)
                    if url.startswith('//'):
                        url = 'https:' + url
                    # Remove tracking parameters
                    url = re.sub(r'[\?&]ref=[^&]*', '', url)
                    url = re.sub(r'[\?&]utm_[^&]*=[^&]*', '', url)
                    logger.info(f"  ✅ Found via JS redirect: {url}")
                    return url
                
                # Look for the actual website link in common patterns
                selectors = [
                    'a[href*="http"]:not([href*="futuretools"])',
                    'a.external-link',
                    'a[target="_blank"]',
                    'a[rel="nofollow"]',
                    '.tool-website a',
                    '.visit-site a'
                ]
                
                for selector in selectors:
                    links = soup.select(selector)
                    for link in links:
                        href = link.get('href', '')
                        if href and 'http' in href and 'futuretools' not in href:
                            # Clean the URL
                            if href.startswith('//'):
                                href = 'https:' + href
                            # Remove tracking parameters
                            href = re.sub(r'[\?&]ref=[^&]*', '', href)
                            href = re.sub(r'[\?&]utm_[^&]*=[^&]*', '', href)
                            logger.info(f"  ✅ Found actual website: {href}")
                            return href
            
            logger.warning(f"  ⚠️ Could not resolve actual website for: {futuretools_url}")
            return futuretools_url
            
        except Exception as e:
            logger.error(f"  ❌ Error resolving URL: {str(e)}")
            return futuretools_url

    def extract_comprehensive_website_data(self, url):
        """Extract comprehensive data from the actual website"""
        try:
            logger.info(f"  📊 Extracting website data from: {url}")
            
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract title
                title = soup.find('title')
                title_text = title.get_text().strip() if title else ""
                
                # Extract meta description
                meta_desc = soup.find('meta', attrs={'name': 'description'})
                description = meta_desc.get('content', '').strip() if meta_desc else ""
                
                # Extract Open Graph data
                og_title = soup.find('meta', property='og:title')
                og_description = soup.find('meta', property='og:description')
                og_image = soup.find('meta', property='og:image')
                
                # Extract logo
                logo_url = self.extract_logo_from_website(soup, url)
                
                # Extract headings for content analysis
                headings = []
                for h in soup.find_all(['h1', 'h2', 'h3']):
                    text = h.get_text().strip()
                    if text and len(text) < 100:
                        headings.append(text)
                
                # Extract paragraphs for content
                paragraphs = []
                for p in soup.find_all('p'):
                    text = p.get_text().strip()
                    if text and len(text) > 20 and len(text) < 300:
                        paragraphs.append(text)
                
                return {
                    'title': title_text,
                    'meta_description': description,
                    'og_title': og_title.get('content', '') if og_title else '',
                    'og_description': og_description.get('content', '') if og_description else '',
                    'og_image': og_image.get('content', '') if og_image else '',
                    'logo_url': logo_url,
                    'headings': headings[:5],  # Top 5 headings
                    'paragraphs': paragraphs[:3],  # Top 3 paragraphs
                    'domain': urlparse(url).netloc
                }
            
            logger.warning(f"  ⚠️ Could not extract data from {url}: Status {response.status_code}")
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ Error extracting website data: {str(e)}")
            return {}

    def extract_logo_from_website(self, soup, base_url):
        """Extract logo URL from website"""
        try:
            # Try multiple logo detection methods
            logo_selectors = [
                'img[alt*="logo" i]',
                '.logo img',
                'header img',
                '.navbar img',
                '.brand img',
                'img[src*="logo" i]',
                'link[rel="icon"]',
                'link[rel="apple-touch-icon"]'
            ]
            
            for selector in logo_selectors:
                elements = soup.select(selector)
                for element in elements:
                    if element.name == 'img':
                        src = element.get('src', '')
                    elif element.name == 'link':
                        src = element.get('href', '')
                    else:
                        continue
                    
                    if src:
                        if src.startswith('//'):
                            src = 'https:' + src
                        elif src.startswith('/'):
                            src = urljoin(base_url, src)
                        
                        if src.startswith('http'):
                            return src
            
            # Fallback to Google favicon service
            domain = urlparse(base_url).netloc
            return f"https://www.google.com/s2/favicons?sz=128&domain={domain}"
            
        except Exception as e:
            logger.warning(f"Error extracting logo: {str(e)}")
            domain = urlparse(base_url).netloc
            return f"https://www.google.com/s2/favicons?sz=128&domain={domain}"

    def create_quality_descriptions(self, tool_name, website_data):
        """Create high-quality descriptions from website data"""
        
        # Get the best available description
        descriptions = [
            website_data.get('meta_description', ''),
            website_data.get('og_description', ''),
            website_data.get('title', ''),
            website_data.get('og_title', '')
        ]
        
        # Filter and clean descriptions
        good_descriptions = []
        for desc in descriptions:
            if desc and len(desc) > 20 and tool_name.lower() not in desc.lower():
                cleaned = re.sub(r'\s+', ' ', desc).strip()
                if cleaned and len(cleaned) > 20:
                    good_descriptions.append(cleaned)
        
        # Create short description (150 chars max)
        if good_descriptions:
            short_desc = good_descriptions[0]
            if len(short_desc) > 150:
                short_desc = short_desc[:147] + "..."
        else:
            # Fallback based on tool name analysis
            name_lower = tool_name.lower()
            if 'ai' in name_lower:
                short_desc = f"{tool_name} is an artificial intelligence platform that provides advanced automation and intelligent solutions."
            else:
                short_desc = f"{tool_name} is a professional software solution designed to enhance productivity and streamline workflows."
        
        # Create long description (300-500 chars)
        long_desc_parts = []
        
        # Start with the best description
        if good_descriptions:
            long_desc_parts.append(good_descriptions[0])
        
        # Add context from headings
        headings = website_data.get('headings', [])
        relevant_headings = [h for h in headings if len(h) > 10 and len(h) < 80]
        if relevant_headings:
            long_desc_parts.append(f"Key features include {', '.join(relevant_headings[:2])}.")
        
        # Add context from paragraphs
        paragraphs = website_data.get('paragraphs', [])
        if paragraphs:
            for para in paragraphs:
                if len(para) > 50 and len(para) < 200:
                    long_desc_parts.append(para)
                    break
        
        # Combine and clean
        long_desc = ' '.join(long_desc_parts)
        long_desc = re.sub(r'\s+', ' ', long_desc).strip()
        
        # Ensure reasonable length
        if len(long_desc) > 500:
            long_desc = long_desc[:497] + "..."
        elif len(long_desc) < 100:
            long_desc = short_desc + f" The platform is designed for professional use and offers comprehensive tools to help users achieve their goals efficiently."
        
        return short_desc, long_desc

    def smart_categorize_tool(self, tool_name, website_data, actual_url):
        """Intelligently categorize tool based on comprehensive analysis"""
        
        # Combine all text for analysis
        all_text = ' '.join([
            tool_name,
            website_data.get('title', ''),
            website_data.get('meta_description', ''),
            ' '.join(website_data.get('headings', [])),
            ' '.join(website_data.get('paragraphs', []))
        ]).lower()
        
        categories = []
        tags = []
        features = []
        
        # Smart categorization based on content analysis
        if any(word in all_text for word in ['chat', 'conversation', 'assistant', 'gpt', 'bot']):
            categories.extend(['AI Assistants', 'Communication'])
            tags.extend(['Conversational AI', 'Virtual Assistant', 'Natural Language Processing'])
            features.extend(['Chat Interface', 'Natural Language Understanding'])
            
        elif any(word in all_text for word in ['image', 'photo', 'visual', 'design', 'creative']):
            categories.extend(['Computer Vision', 'Content Creation'])
            tags.extend(['Image Processing', 'Creative Tools', 'Visual AI'])
            features.extend(['Image Generation', 'Visual Recognition', 'Creative Suite'])
            
        elif any(word in all_text for word in ['data', 'analytics', 'insight', 'report', 'dashboard']):
            categories.extend(['Analytics', 'Business Intelligence'])
            tags.extend(['Data Analysis', 'Business Intelligence', 'Reporting'])
            features.extend(['Data Visualization', 'Analytics Dashboard', 'Report Generation'])
            
        elif any(word in all_text for word in ['content', 'write', 'text', 'blog', 'article']):
            categories.extend(['Content Creation', 'Marketing'])
            tags.extend(['Content Generation', 'Writing Assistant', 'Marketing Tools'])
            features.extend(['Text Generation', 'Content Optimization', 'SEO Tools'])
            
        elif any(word in all_text for word in ['code', 'developer', 'programming', 'api', 'software']):
            categories.extend(['Development', 'Automation'])
            tags.extend(['Developer Tools', 'Code Generation', 'API'])
            features.extend(['Code Generation', 'API Integration', 'Development Framework'])
            
        else:
            categories.append('Business & Productivity')
            tags.extend(['Business Tools', 'Productivity'])
            features.extend(['Automation', 'Business Process'])
        
        # Add common tags
        tags.extend(['AI-Powered', 'Professional', 'Cloud-Based'])
        features.extend(['User-Friendly Interface', 'Professional Support'])
        
        return categories, tags, features

    def process_tool(self, tool_data):
        """Process a single tool with quality standards"""
        tool_name = tool_data.get('tool_name_on_directory', '')
        futuretools_url = tool_data.get('external_website_url', '')
        
        logger.info(f"🚀 Processing: {tool_name}")
        
        # Step 1: Resolve to actual website
        actual_url = self.resolve_to_actual_website(futuretools_url)
        
        # Step 2: Get entity type
        entity_type = self.entity_detector.detect_entity_type(tool_name, actual_url)
        entity_type_id = self.get_entity_type_id(entity_type)
        
        if not entity_type_id:
            logger.error(f"  ❌ Could not get entity type ID")
            return None
        
        # Step 3: Extract comprehensive website data
        website_data = self.extract_comprehensive_website_data(actual_url)
        
        # Step 4: Create quality descriptions
        short_desc, long_desc = self.create_quality_descriptions(tool_name, website_data)
        
        # Step 5: Smart categorization
        categories, tags, features = self.smart_categorize_tool(tool_name, website_data, actual_url)
        
        # Step 6: Map to existing taxonomy
        category_ids = self.taxonomy_service.map_categories(categories)
        tag_ids = self.taxonomy_service.map_tags(tags)
        feature_ids = self.taxonomy_service.map_features(features)
        
        # Ensure at least one category (API requirement)
        if not category_ids:
            # Fallback to Business & Productivity
            business_cat = self.taxonomy_service.categories_map.get('business & productivity')
            if business_cat:
                category_ids = [business_cat['id']]
            else:
                # If that doesn't exist, use the first available category
                if self.taxonomy_service.categories_map:
                    first_cat = list(self.taxonomy_service.categories_map.values())[0]
                    category_ids = [first_cat['id']]
        
        logger.info(f"  📊 Quality Check:")
        logger.info(f"    • Actual URL: {actual_url != futuretools_url}")
        logger.info(f"    • Description: {len(long_desc)} chars")
        logger.info(f"    • Taxonomy: {len(category_ids)}C, {len(tag_ids)}T, {len(feature_ids)}F")
        
        # Step 7: Build high-quality entity
        entity_dto = {
            'name': tool_name,
            'website_url': actual_url,  # ACTUAL WEBSITE, NOT FUTURETOOLS
            'entity_type_id': entity_type_id,
            'short_description': short_desc,
            'description': long_desc,
            'category_ids': category_ids,
            'tag_ids': tag_ids,
            'feature_ids': feature_ids,
            'meta_title': f"{tool_name} | AI Navigator",
            'meta_description': short_desc,
            'ref_link': actual_url,
            'affiliate_status': 'NONE',
            'status': 'PENDING',
            'tool_details': {
                'learning_curve': 'MEDIUM',
                'key_features': features[:8],
                'has_free_tier': True,
                'use_cases': ['Professional Applications', 'Business Automation'],
                'pricing_model': 'FREEMIUM',
                'price_range': 'MEDIUM',
                'target_audience': ['Business Professionals', 'Teams'],
                'mobile_support': False,
                'api_access': 'api' in ' '.join(features).lower(),
                'customization_level': 'Medium',
                'trial_available': True,
                'demo_available': False,
                'open_source': False,
                'support_channels': ['Email', 'Documentation']
            }
        }
        
        # Add logo if found
        if website_data.get('logo_url'):
            entity_dto['logo_url'] = website_data['logo_url']
        
        return entity_dto

    def submit_entity(self, entity_dto):
        """Submit entity to database"""
        try:
            result = self.client.create_entity(entity_dto)
            if result:
                logger.info(f"  ✅ Successfully created: {entity_dto['name']}")
                return True
            else:
                logger.error(f"  ❌ Failed to create: {entity_dto['name']}")
                return False
        except Exception as e:
            logger.error(f"  ❌ Error submitting: {str(e)}")
            return False

def main():
    processor = ImprovedProcessor()
    
    # Load tools (skip the first 10 we already processed)
    tools_file = '/app/ai-navigator-scrapers/futuretools_leads.jsonl'
    tools = []
    
    with open(tools_file, 'r') as f:
        for line in f:
            tools.append(json.loads(line.strip()))
    
    # Process tools 11-20 (next 10)
    tools_to_process = tools[10:20]
    
    logger.info(f"📊 Processing tools 11-20 with QUALITY STANDARDS...")
    
    success_count = 0
    
    for i, tool in enumerate(tools_to_process):
        logger.info(f"\n{'='*60}")
        logger.info(f"Processing tool {i+11}/{i+20}")
        
        try:
            entity_dto = processor.process_tool(tool)
            if entity_dto:
                success = processor.submit_entity(entity_dto)
                if success:
                    success_count += 1
                time.sleep(2)  # Be respectful to websites
            else:
                logger.error(f"Failed to process: {tool.get('tool_name_on_directory', 'Unknown')}")
                
        except Exception as e:
            logger.error(f"Error: {str(e)}")
    
    logger.info(f"\n{'='*60}")
    logger.info(f"🎉 QUALITY PROCESSING COMPLETE!")
    logger.info(f"✅ Successfully processed: {success_count}/10 tools")
    logger.info(f"📊 Success rate: {(success_count/10)*100:.1f}%")

if __name__ == "__main__":
    main()