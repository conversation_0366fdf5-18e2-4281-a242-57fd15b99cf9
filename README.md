# AI Navigator Scrapers

A comprehensive web scraping and data enrichment system for collecting AI tool information from various online directories and populating the AI Navigator database.

## 🚀 Features

- **Multi-Source Scraping**: Collect data from multiple AI tool directories
- **Data Enrichment**: Enhance scraped data with AI-powered analysis
- **Taxonomy Management**: Intelligent categorization and tagging
- **API Integration**: RESTful API for managing scraping jobs
- **Web Interface**: React-based frontend for monitoring and control
- **Secure Configuration**: Environment-based secrets management
- **Comprehensive Logging**: Structured logging with error tracking
- **Error Handling**: Robust error handling and retry mechanisms

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Documentation](#api-documentation)
- [Architecture](#architecture)
- [Development](#development)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## 🔧 Prerequisites

- Python 3.8+
- Node.js 16+
- PostgreSQL (for AI Navigator database)
- API Keys:
  - XAI API Key
  - Perplexity API Key
  - AI Navigator admin credentials

## 📦 Installation

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/columj9/ai-navigator-scrapers.git
cd ai-navigator-scrapers
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt
```

### 3. Set Up Frontend

```bash
cd frontend
npm install
cd ..
```

### 4. Install Playwright (for browser automation)

```bash
playwright install
```

## ⚙️ Configuration

### 1. Environment Variables

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

```env
# API Keys (Required)
XAI_API_KEY=your_xai_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# AI Navigator Backend (Required)
AI_NAVIGATOR_BASE_URL=https://ai-nav.onrender.com
AI_NAVIGATOR_ADMIN_EMAIL=<EMAIL>
AI_NAVIGATOR_ADMIN_PASSWORD=your_secure_password

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8001
CORS_ORIGINS=http://localhost:3000

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
```

### 2. Required API Keys

#### XAI API Key
1. Visit [XAI Console](https://console.x.ai/)
2. Create an account and generate an API key
3. Add to your `.env` file

#### Perplexity API Key
1. Visit [Perplexity API](https://www.perplexity.ai/settings/api)
2. Create an account and generate an API key
3. Add to your `.env` file

#### AI Navigator Credentials
1. Contact your AI Navigator administrator for credentials
2. Add admin email and password to your `.env` file

## 🚀 Usage

### Starting the Application

#### 1. Start the Backend Server

```bash
cd backend
python server.py
```

The API server will start on `http://localhost:8001`

#### 2. Start the Frontend (Optional)

```bash
cd frontend
npm start
```

The web interface will be available at `http://localhost:3000`

### Running Scrapers

#### Command Line Usage

```bash
# Run a specific scraper
python working_sources_tester.py

# Run multi-source processor
python working_multi_source.py

# Run cost-optimized processor
python cost_optimized_processor.py
```

#### Using the API

```bash
# Start a scraping job
curl -X POST http://localhost:8001/api/scraping/start \
  -H "Content-Type: application/json" \
  -d '{"spider_name": "futuretools", "max_items": 50}'

# Check job status
curl http://localhost:8001/api/scraping/status

# Get results
curl http://localhost:8001/api/scraping-results/futuretools
```

### Available Scrapers

- **futuretools**: Scrapes FutureTools.io
- **taaft**: Scrapes TheresAnAIForThat.com
- **toolify**: Scrapes Toolify.ai (requires browser automation)

## 📚 API Documentation

### Endpoints

#### Health Check
```
GET /api/health
```

#### Start Scraping Job
```
POST /api/scraping/start
Content-Type: application/json

{
  "spider_name": "futuretools",
  "max_items": 50
}
```

#### Get Job Status
```
GET /api/scraping/status
```

#### Get Scraping Results
```
GET /api/scraping-results/{spider_name}
```

#### Get Missing Taxonomy
```
GET /api/missing-taxonomy
```

### Response Formats

All API responses follow this structure:

```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🏗️ Architecture

### Project Structure

```
ai-navigator-scrapers/
├── backend/                 # FastAPI backend server
├── frontend/               # React frontend application
├── ai-navigator-scrapers/  # Scrapy project
│   └── ainav_scrapers/
│       ├── spiders/        # Scrapy spiders
│       ├── items.py        # Data models
│       ├── pipelines.py    # Data processing pipelines
│       └── settings.py     # Scrapy configuration
├── shared/                 # Shared utilities and modules
│   ├── base_processor.py   # Base processor class
│   ├── utils.py           # Common utilities
│   ├── error_handling.py  # Error handling framework
│   └── logging_config.py  # Logging configuration
├── config.py              # Configuration management
├── ai_navigator_client.py # AI Navigator API client
├── data_enrichment_service.py # Data enrichment
├── enhanced_taxonomy_service.py # Taxonomy management
└── scraper_pipeline.py    # Main scraping pipeline
```

### Data Flow

1. **Scraping**: Scrapy spiders collect raw data from target websites
2. **Processing**: Data is cleaned and normalized
3. **Enrichment**: AI APIs enhance data with additional information
4. **Taxonomy**: Tools are categorized and tagged
5. **Storage**: Processed data is sent to AI Navigator database

### Key Components

- **Scrapy Spiders**: Web scraping engines for different sources
- **Data Enrichment**: AI-powered data enhancement using XAI and Perplexity
- **Taxonomy Service**: Intelligent categorization and tagging
- **Error Handling**: Comprehensive error tracking and recovery
- **Configuration**: Secure environment-based configuration
- **Logging**: Structured logging with multiple output formats

## 🛠️ Development

### Setting Up Development Environment

1. **Install development dependencies**:
```bash
pip install -r requirements.txt
pip install pytest pytest-asyncio black flake8
```

2. **Set up pre-commit hooks** (optional):
```bash
pip install pre-commit
pre-commit install
```

3. **Run tests**:
```bash
pytest
```

### Code Style

- **Python**: Follow PEP 8 guidelines
- **JavaScript**: Use ESLint and Prettier
- **Formatting**: Use Black for Python, Prettier for JavaScript

### Adding New Scrapers

1. Create a new spider in `ai-navigator-scrapers/ainav_scrapers/spiders/`
2. Define data extraction logic
3. Add spider configuration to settings
4. Test thoroughly with small datasets
5. Update documentation

### Environment Variables for Development

```env
# Development settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Use local directories for development
SCRAPY_DIR=./ai-navigator-scrapers
OUTPUT_DIR=./ai-navigator-scrapers
LOG_DIR=./logs

# Development CORS (allow all origins)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

## 🚀 Deployment

### Production Environment

#### 1. Server Requirements

- **CPU**: 2+ cores
- **RAM**: 4GB+ recommended
- **Storage**: 20GB+ for logs and data
- **Network**: Stable internet connection

#### 2. Environment Configuration

```env
# Production settings
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Production CORS (specific origins only)
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Production paths
SCRAPY_DIR=/app/ai-navigator-scrapers
OUTPUT_DIR=/app/data
LOG_DIR=/app/logs
```

#### 3. Docker Deployment (Recommended)

Create `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright
RUN playwright install chromium

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Expose port
EXPOSE 8001

# Start application
CMD ["python", "backend/server.py"]
```

Build and run:

```bash
docker build -t ai-navigator-scrapers .
docker run -d --env-file .env -p 8001:8001 ai-navigator-scrapers
```

#### 4. Process Management

Use a process manager like PM2 or systemd:

```bash
# Using PM2
npm install -g pm2
pm2 start backend/server.py --name ai-scrapers --interpreter python3
pm2 startup
pm2 save
```

### Monitoring

- **Logs**: Monitor application logs in `/app/logs/`
- **Health Check**: Use `/api/health` endpoint
- **Metrics**: Track scraping success rates and error counts
- **Alerts**: Set up alerts for critical errors

## 🔧 Troubleshooting

### Common Issues

#### 1. API Key Errors

**Problem**: `XAI_API_KEY is required` or similar errors

**Solution**:
- Verify API keys are set in `.env` file
- Check API key validity and quotas
- Ensure no extra spaces or quotes in `.env` file

#### 2. Database Connection Issues

**Problem**: Cannot connect to AI Navigator database

**Solution**:
- Verify `AI_NAVIGATOR_BASE_URL` is correct
- Check admin credentials
- Test network connectivity to the API

#### 3. Scraping Failures

**Problem**: Scrapers fail to extract data

**Solution**:
- Check if target websites have changed structure
- Verify robots.txt compliance
- Adjust rate limiting settings
- Check for IP blocking

#### 4. Memory Issues

**Problem**: High memory usage during scraping

**Solution**:
- Reduce `SCRAPING_CONCURRENT_REQUESTS`
- Increase `SCRAPING_MIN_DELAY` and `SCRAPING_MAX_DELAY`
- Monitor and restart processes if needed

### Debugging

#### Enable Debug Logging

```env
LOG_LEVEL=DEBUG
DEBUG=true
```

#### Check Logs

```bash
# View recent logs
tail -f logs/scraper_pipeline.log

# View error logs only
tail -f logs/scraper_pipeline_errors.log
```

#### Test Individual Components

```bash
# Test configuration
python -c "from config import config; print(config.api.xai_api_key[:10] + '...')"

# Test AI Navigator connection
python -c "from ai_navigator_client import AINavigatorClient; client = AINavigatorClient(); print('Connected!')"
```

## 🤝 Contributing

### Getting Started

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `pytest`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Guidelines

- Follow existing code style and patterns
- Add comprehensive tests for new features
- Update documentation for any API changes
- Ensure backward compatibility when possible
- Add appropriate error handling and logging

### Reporting Issues

When reporting issues, please include:

- Python version and OS
- Full error messages and stack traces
- Steps to reproduce the issue
- Expected vs actual behavior
- Relevant configuration (without sensitive data)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Scrapy](https://scrapy.org/) - Web scraping framework
- [FastAPI](https://fastapi.tiangolo.com/) - Modern web framework
- [React](https://reactjs.org/) - Frontend framework
- [XAI](https://x.ai/) - AI API for data enrichment
- [Perplexity](https://www.perplexity.ai/) - AI API for research

## 📞 Support

For support and questions:

- Create an issue on GitHub
- Check the [troubleshooting section](#troubleshooting)
- Review the [API documentation](#api-documentation)

---

**Note**: This project is designed to work with the AI Navigator platform. Ensure you have proper access and credentials before setting up the scrapers.