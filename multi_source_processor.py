"""
Multi-Source AI Tool Processor
- Fixed XAI integration
- Toolify scraper with Cloudflare bypass
- Product Hunt AI tools
- GitHub AI repositories
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, quote
import re
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiSourceProcessor:
    def __init__(self, xai_api_key: str, perplexity_api_key: str):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.xai_api_key = xai_api_key
        self.perplexity_api_key = perplexity_api_key
        
        # User agents for rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
    
    def scrape_toolify_tools(self, max_tools: int = 50) -> list:
        """Scrape tools from Toolify with Cloudflare bypass"""
        logger.info(f"🔍 Scraping Toolify.ai for up to {max_tools} tools...")
        
        # Try multiple approaches for Cloudflare bypass
        tools = []
        
        # Approach 1: Try with different endpoints
        toolify_endpoints = [
            "https://www.toolify.ai/",
            "https://toolify.ai/browse",
            "https://toolify.ai/categories/ai-tools",
            "https://toolify.ai/ai-tools"
        ]
        
        for endpoint in toolify_endpoints:
            try:
                logger.info(f"  🌐 Trying endpoint: {endpoint}")
                scraped = self._scrape_toolify_endpoint(endpoint)
                if scraped:
                    tools.extend(scraped)
                    logger.info(f"  ✅ Found {len(scraped)} tools from {endpoint}")
                    if len(tools) >= max_tools:
                        break
                time.sleep(random.uniform(2, 4))  # Random delay
            except Exception as e:
                logger.warning(f"  ⚠️ Failed to scrape {endpoint}: {str(e)}")
                continue
        
        # Approach 2: If direct scraping fails, try selenium
        if not tools:
            tools = self._scrape_toolify_with_selenium(max_tools)
        
        # Approach 3: If all fails, use alternative sources
        if not tools:
            logger.warning("  ⚠️ Toolify direct scraping failed, using alternative discovery")
            tools = self._discover_toolify_alternatives()
        
        logger.info(f"🎉 Total Toolify tools discovered: {len(tools)}")
        return tools[:max_tools]
    
    def _scrape_toolify_endpoint(self, url: str) -> list:
        """Scrape a specific Toolify endpoint"""
        try:
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                return self._extract_tools_from_toolify_page(soup, url)
            else:
                logger.warning(f"  ⚠️ Status {response.status_code} for {url}")
                return []
                
        except Exception as e:
            logger.warning(f"  ⚠️ Error scraping {url}: {str(e)}")
            return []
    
    def _extract_tools_from_toolify_page(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract tools from Toolify page"""
        tools = []
        
        # Try multiple selectors for tool cards
        tool_selectors = [
            '.tool-card',
            '.ai-tool-card', 
            '[data-testid="tool-card"]',
            '.grid-item',
            '.tool-item',
            'a[href*="/tool/"]',
            'a[href*="/ai-tools/"]'
        ]
        
        for selector in tool_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"  📦 Found {len(elements)} tools with selector: {selector}")
                for element in elements[:50]:  # Limit per selector
                    tool_data = self._extract_tool_data_from_element(element, base_url)
                    if tool_data:
                        tools.append(tool_data)
                break
        
        # If no specific selectors work, try extracting from links
        if not tools:
            tools = self._extract_tools_from_links(soup, base_url)
        
        return tools
    
    def _extract_tool_data_from_element(self, element, base_url: str) -> dict:
        """Extract tool data from a single element"""
        try:
            # Extract name
            name_selectors = ['h3', 'h2', '.tool-name', '.title', 'a']
            name = ""
            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    name = name_elem.get_text().strip()
                    if len(name) > 3:  # Valid name length
                        break
            
            # Extract URL
            url = ""
            link_elem = element.find('a')
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    url = urljoin(base_url, href)
                elif href.startswith('http'):
                    url = href
            
            # Extract description
            desc_selectors = ['.description', '.summary', 'p', '.tool-desc']
            description = ""
            for selector in desc_selectors:
                desc_elem = element.select_one(selector)
                if desc_elem:
                    description = desc_elem.get_text().strip()
                    if len(description) > 10:
                        break
            
            # Extract category
            category_selectors = ['.category', '.tag', '.badge']
            category = ""
            for selector in category_selectors:
                cat_elem = element.select_one(selector)
                if cat_elem:
                    category = cat_elem.get_text().strip()
                    break
            
            if name and len(name) > 2:
                return {
                    'tool_name_on_directory': name,
                    'external_website_url': url or f"https://toolify.ai/tool/{quote(name.lower())}",
                    'description': description,
                    'category': category,
                    'source': 'toolify'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"  ⚠️ Error extracting tool data: {str(e)}")
            return None
    
    def _extract_tools_from_links(self, soup: BeautifulSoup, base_url: str) -> list:
        """Fallback: extract tools from all relevant links"""
        tools = []
        
        # Find links that might be tools
        links = soup.find_all('a', href=True)
        for link in links:
            href = link.get('href', '')
            text = link.get_text().strip()
            
            # Check if this looks like a tool link
            if (('/tool/' in href or '/ai-tools/' in href) and 
                text and len(text) > 3 and len(text) < 100):
                
                # Build full URL
                if href.startswith('/'):
                    full_url = urljoin(base_url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue
                
                tools.append({
                    'tool_name_on_directory': text,
                    'external_website_url': full_url,
                    'description': '',
                    'category': '',
                    'source': 'toolify'
                })
                
                if len(tools) >= 50:  # Limit fallback results
                    break
        
        return tools
    
    def _scrape_toolify_with_selenium(self, max_tools: int) -> list:
        """Use Selenium for JavaScript-heavy sites"""
        logger.info("  🤖 Trying Selenium approach for Toolify...")
        tools = []
        
        try:
            # Setup Chrome with options to bypass detection
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument(f'--user-agent={random.choice(self.user_agents)}')
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Try to access Toolify
            driver.get("https://toolify.ai/")
            
            # Wait for content to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page source and parse
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            tools = self._extract_tools_from_toolify_page(soup, "https://toolify.ai/")
            
            driver.quit()
            logger.info(f"  ✅ Selenium found {len(tools)} tools")
            
        except Exception as e:
            logger.warning(f"  ⚠️ Selenium approach failed: {str(e)}")
            try:
                driver.quit()
            except:
                pass
        
        return tools[:max_tools]
    
    def _discover_toolify_alternatives(self) -> list:
        """Discover tools from alternative sources when Toolify is blocked"""
        logger.info("  🔍 Discovering tools from alternative sources...")
        tools = []
        
        # Alternative 1: Product Hunt AI tools
        ph_tools = self.scrape_product_hunt_ai_tools(25)
        tools.extend(ph_tools)
        
        # Alternative 2: GitHub AI repositories
        github_tools = self.scrape_github_ai_repositories(25)
        tools.extend(github_tools)
        
        return tools
    
    def scrape_product_hunt_ai_tools(self, max_tools: int = 25) -> list:
        """Scrape AI tools from Product Hunt"""
        logger.info(f"🔍 Scraping Product Hunt AI tools...")
        tools = []
        
        try:
            # Product Hunt AI tools pages
            ph_urls = [
                "https://www.producthunt.com/topics/artificial-intelligence",
                "https://www.producthunt.com/search?q=AI%20tool",
                "https://www.producthunt.com/search?q=machine%20learning"
            ]
            
            for url in ph_urls:
                try:
                    headers = {'User-Agent': random.choice(self.user_agents)}
                    response = requests.get(url, headers=headers, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        page_tools = self._extract_product_hunt_tools(soup)
                        tools.extend(page_tools)
                        logger.info(f"  ✅ Found {len(page_tools)} tools from Product Hunt")
                        
                        if len(tools) >= max_tools:
                            break
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"  ⚠️ Error scraping {url}: {str(e)}")
                    continue
            
        except Exception as e:
            logger.warning(f"  ⚠️ Product Hunt scraping failed: {str(e)}")
        
        return tools[:max_tools]
    
    def _extract_product_hunt_tools(self, soup: BeautifulSoup) -> list:
        """Extract tools from Product Hunt page"""
        tools = []
        
        # Product Hunt uses various selectors
        selectors = [
            '[data-test="post-item"]',
            '.post-item',
            'a[href*="/posts/"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                for element in elements[:20]:
                    tool_data = self._extract_ph_tool_data(element)
                    if tool_data:
                        tools.append(tool_data)
                break
        
        return tools
    
    def _extract_ph_tool_data(self, element) -> dict:
        """Extract tool data from Product Hunt element"""
        try:
            # Extract name
            name_elem = element.select_one('h3, .post-title, [data-test="post-name"]')
            name = name_elem.get_text().strip() if name_elem else ""
            
            # Extract URL from href
            link_elem = element.find('a')
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    url = f"https://www.producthunt.com{href}"
                else:
                    url = href
            else:
                url = ""
            
            # Extract description
            desc_elem = element.select_one('.post-description, p')
            description = desc_elem.get_text().strip() if desc_elem else ""
            
            if name and len(name) > 2:
                return {
                    'tool_name_on_directory': name,
                    'external_website_url': url,
                    'description': description,
                    'category': 'Product Hunt Discovery',
                    'source': 'product_hunt'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"  ⚠️ Error extracting PH tool: {str(e)}")
            return None
    
    def scrape_github_ai_repositories(self, max_tools: int = 25) -> list:
        """Scrape AI tools from GitHub"""
        logger.info(f"🔍 Scraping GitHub AI repositories...")
        tools = []
        
        try:
            # GitHub search queries for AI tools
            queries = [
                "ai+tool+language:python",
                "machine+learning+framework",
                "artificial+intelligence+api",
                "ai+assistant+tool"
            ]
            
            for query in queries:
                try:
                    url = f"https://github.com/search?q={query}&type=repositories&sort=stars"
                    headers = {'User-Agent': random.choice(self.user_agents)}
                    response = requests.get(url, headers=headers, timeout=15)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        page_tools = self._extract_github_tools(soup)
                        tools.extend(page_tools)
                        logger.info(f"  ✅ Found {len(page_tools)} repos from GitHub")
                        
                        if len(tools) >= max_tools:
                            break
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"  ⚠️ Error scraping GitHub: {str(e)}")
                    continue
            
        except Exception as e:
            logger.warning(f"  ⚠️ GitHub scraping failed: {str(e)}")
        
        return tools[:max_tools]
    
    def _extract_github_tools(self, soup: BeautifulSoup) -> list:
        """Extract tools from GitHub search results"""
        tools = []
        
        # GitHub repository selectors
        repo_elements = soup.select('.repo-list-item, [data-testid="results-list"] > div')
        
        for element in repo_elements[:15]:
            try:
                # Extract repository name
                name_elem = element.select_one('a[data-testid="results-list-repo-path"], .v-align-middle')
                if not name_elem:
                    continue
                
                repo_name = name_elem.get_text().strip()
                repo_url = name_elem.get('href', '')
                
                if repo_url.startswith('/'):
                    repo_url = f"https://github.com{repo_url}"
                
                # Extract description
                desc_elem = element.select_one('p, .mb-1')
                description = desc_elem.get_text().strip() if desc_elem else ""
                
                # Extract language
                lang_elem = element.select_one('[itemprop="programmingLanguage"]')
                language = lang_elem.get_text().strip() if lang_elem else ""
                
                if repo_name and repo_url:
                    tools.append({
                        'tool_name_on_directory': repo_name,
                        'external_website_url': repo_url,
                        'description': description,
                        'category': f'Open Source ({language})' if language else 'Open Source',
                        'source': 'github'
                    })
                
            except Exception as e:
                logger.warning(f"  ⚠️ Error extracting GitHub repo: {str(e)}")
                continue
        
        return tools
    
    def process_tool(self, tool_data: dict) -> bool:
        """Process a single tool with enhanced XAI"""
        tool_name = tool_data.get('tool_name_on_directory', '')
        external_url = tool_data.get('external_website_url', '')
        source = tool_data.get('source', 'unknown')
        
        logger.info(f"🚀 Processing: {tool_name} (from {source})")
        
        try:
            # Step 1: Resolve actual website URL (if needed)
            if source == 'futuretools':
                actual_url = self._resolve_futuretools_url(external_url)
            elif source in ['product_hunt', 'github']:
                actual_url = self._resolve_external_source_url(external_url, tool_name)
            else:
                actual_url = external_url
            
            logger.info(f"  🔍 Resolved URL: {actual_url}")
            
            # Step 2: Check if entity exists
            existing_entity = self._find_existing_entity(tool_name, actual_url)
            
            # Step 3: Scrape website comprehensively
            website_data = self._scrape_website(actual_url)
            
            # Step 4: Enhanced AI pipeline with working XAI
            enhanced_data = self._enhance_with_ai_pipeline(tool_name, website_data, actual_url)
            
            # Step 5: Build entity with validation
            entity_data = self._build_validated_entity(tool_name, actual_url, website_data, enhanced_data)
            
            # Step 6: Create or update
            if existing_entity:
                result = self._update_entity(existing_entity['id'], entity_data)
                action = "Updated"
            else:
                result = self._create_entity(entity_data)
                action = "Created"
            
            if result:
                logger.info(f"  ✅ {action} entity: {tool_name}")
                return True
            else:
                logger.error(f"  ❌ Failed to {action.lower()} entity: {tool_name}")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ Error processing {tool_name}: {str(e)}")
            return False
    
    # Use the same helper methods from working_processor.py for consistency
    def _resolve_futuretools_url(self, futuretools_url: str) -> str:
        """Resolve FutureTools redirect to actual website"""
        try:
            response = requests.get(futuretools_url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            # Check for meta refresh redirect
            meta_refresh = re.search(r'<meta[^>]*http-equiv=["\']refresh["\'][^>]*content=["\'][^>]*url=([^"\'>\s]+)', response.text, re.IGNORECASE)
            if meta_refresh:
                url = meta_refresh.group(1)
                # Remove tracking parameters
                url = re.sub(r'[\?&]ref=[^&]*', '', url)
                url = re.sub(r'[\?&]utm_[^&]*=[^&]*', '', url)
                return url
            
            return futuretools_url
            
        except Exception as e:
            logger.warning(f"Error resolving URL: {str(e)}")
            return futuretools_url
    
    def _resolve_external_source_url(self, source_url: str, tool_name: str) -> str:
        """Resolve external source URLs to actual tool websites"""
        try:
            # For Product Hunt, try to find the actual website
            if 'producthunt.com' in source_url:
                response = requests.get(source_url, timeout=15, headers={
                    'User-Agent': random.choice(self.user_agents)
                })
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    # Look for website link
                    website_link = soup.select_one('a[href*="http"]:not([href*="producthunt.com"])')
                    if website_link:
                        return website_link.get('href', source_url)
            
            # For GitHub, return the repository URL as-is
            elif 'github.com' in source_url:
                return source_url
            
            return source_url
            
        except Exception as e:
            logger.warning(f"Error resolving external URL: {str(e)}")
            return source_url
    
    # Reuse helper methods from working_processor.py
    def _find_existing_entity(self, name: str, website_url: str) -> dict:
        """Check if entity already exists"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entities",
                headers=self.client._get_headers(),
                params={"search": name, "limit": 10},
                timeout=10
            )
            
            if response.status_code == 200:
                entities = response.json()
                if isinstance(entities, dict) and 'data' in entities:
                    entities = entities['data']
                
                for entity in entities:
                    if (entity.get('name', '').lower() == name.lower() or 
                        entity.get('website_url', '') == website_url):
                        logger.info(f"  🔄 Found existing entity: {entity.get('id')}")
                        return entity
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking existing entity: {str(e)}")
            return None
    
    def _scrape_website(self, url: str) -> dict:
        """Comprehensive website scraping"""
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': random.choice(self.user_agents)
            })
            
            if response.status_code != 200:
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract comprehensive data
            data = {
                'title': self._get_title(soup),
                'meta_description': self._get_meta_description(soup),
                'logo_url': self._extract_logo(soup, url),
                'social_links': self._extract_social_links(response.text),
                'headings': self._extract_headings(soup),
                'paragraphs': self._extract_paragraphs(soup),
                'content_text': soup.get_text()[:2000],  # First 2000 chars
                'domain': urlparse(url).netloc
            }
            
            logger.info(f"  📊 Scraped website successfully")
            return data
            
        except Exception as e:
            logger.warning(f"Error scraping website: {str(e)}")
            return {}
    
    def _enhance_with_ai_pipeline(self, tool_name: str, website_data: dict, url: str) -> dict:
        """AI enhancement with Perplexity primary + XAI when available"""
        
        # Use Perplexity as primary (reliable and cost-effective)
        perplexity_data = self._try_perplexity_enhancement(tool_name, website_data, url)
        
        # Try XAI for additional enhancement if available
        xai_data = self._try_xai_enhancement(tool_name, website_data, url)
        if xai_data:
            # Merge XAI data with Perplexity data (Perplexity takes priority)
            perplexity_data.update({k: v for k, v in xai_data.items() if k not in perplexity_data or not perplexity_data[k]})
        
        # If both fail, create basic data from scraped content
        if not perplexity_data or not perplexity_data.get('short_description'):
            logger.info(f"  🔄 AI failed, creating from scraped data...")
            perplexity_data = self._create_from_scraped_data(tool_name, website_data, url)
        
        return perplexity_data
    
    def _try_xai_enhancement(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Enhanced XAI with proper OpenAI-compatible implementation"""
        try:
            content_summary = f"""
Tool: {tool_name}
Website: {url}
Title: {website_data.get('title', '')}
Description: {website_data.get('meta_description', '')}
Content: {website_data.get('content_text', '')[:1000]}
"""
            
            prompt = f"""
Analyze this tool and extract information. Return ONLY valid JSON:

{content_summary}

{{
  "short_description": "Specific description in 1 sentence (max 150 chars)",
  "description": "Detailed description in 2-3 sentences (max 400 chars)",
  "key_features": ["feature1", "feature2", "feature3"],
  "use_cases": ["use_case1", "use_case2"],
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3"],
  "pricing_model": "FREEMIUM",
  "target_audience": ["audience1", "audience2"]
}}
"""

            response = requests.post(
                "https://api.x.ai/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.xai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "grok-3-beta",  # Correct model name
                    "messages": [
                        {"role": "system", "content": "You are an expert at analyzing websites. Always respond with valid JSON only. Do not include any explanation or markdown formatting."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 1000
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Clean content - remove markdown formatting if present
                if content.startswith('```json'):
                    content = content.replace('```json', '').replace('```', '')
                elif content.startswith('```'):
                    content = content.replace('```', '')
                
                content = content.strip()
                
                # Extract JSON
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = content[json_start:json_end]
                    enhanced_data = json.loads(json_str)
                    logger.info(f"  🤖 XAI enhanced {tool_name} successfully")
                    return enhanced_data
                else:
                    logger.warning(f"  ⚠️ XAI response missing JSON brackets: {content[:100]}")
            else:
                logger.warning(f"  ⚠️ XAI failed with status: {response.status_code}, body: {response.text[:200]}")
            
            return {}
            
        except json.JSONDecodeError as e:
            logger.warning(f"  ⚠️ XAI JSON decode error: {str(e)}")
            return {}
        except Exception as e:
            logger.warning(f"  ⚠️ XAI error: {str(e)}")
            return {}
    
    def _try_perplexity_enhancement(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Try Perplexity enhancement (cost optimized)"""
        try:
            content_summary = f"""
Tool: {tool_name}
Website: {url}
Title: {website_data.get('title', '')}
Description: {website_data.get('meta_description', '')}
Content: {website_data.get('content_text', '')[:800]}
"""
            
            prompt = f"""
Analyze this tool and return JSON:

{content_summary}

{{
  "short_description": "Brief description (max 150 chars)",
  "description": "Detailed description (max 400 chars)",
  "key_features": ["feature1", "feature2", "feature3"],
  "use_cases": ["use_case1", "use_case2"],
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3"],
  "pricing_model": "FREEMIUM",
  "target_audience": ["audience1", "audience2"],
  "founded_year": 2023,
  "employee_count_range": "C1_10"
}}
"""

            response = requests.post(
                "https://api.perplexity.ai/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.perplexity_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "llama-3.1-sonar-small-128k-online",
                    "messages": [
                        {"role": "system", "content": "Provide JSON format responses only."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 800
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    enhanced_data = json.loads(content[json_start:json_end])
                    logger.info(f"  💰 Perplexity enhanced {tool_name}")
                    return enhanced_data
            
            logger.warning(f"  ⚠️ Perplexity failed with status: {response.status_code}")
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ Perplexity error: {str(e)}")
            return {}
    
    def _create_from_scraped_data(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Create basic data from scraped content"""
        
        title = website_data.get('title', '')
        meta_desc = website_data.get('meta_description', '')
        content = website_data.get('content_text', '')
        
        # Create basic descriptions
        if meta_desc and len(meta_desc) > 20:
            short_desc = meta_desc[:150]
            long_desc = meta_desc[:400]
        elif title and len(title) > 10:
            short_desc = f"{tool_name} - {title}"[:150]
            long_desc = f"{tool_name} is a professional tool that provides {title.lower()} capabilities for businesses and individuals."[:400]
        else:
            short_desc = f"{tool_name} is an AI-powered platform for professional use."
            long_desc = f"{tool_name} provides advanced AI capabilities and tools designed to enhance productivity and streamline workflows for modern businesses."
        
        # Basic categorization based on content
        categories = ["AI & Machine Learning"]
        tags = ["AI-Powered", "Professional"]
        features = ["Professional Tools", "User-Friendly Interface"]
        use_cases = ["Business Applications", "Productivity Enhancement"]
        
        # Smart categorization based on content
        content_lower = content.lower()
        if any(word in content_lower for word in ['chat', 'conversation', 'assistant']):
            categories.append("Communication")
            tags.append("Conversational AI")
            features.append("Chat Interface")
        elif any(word in content_lower for word in ['image', 'photo', 'visual']):
            categories.append("Content Creation")
            tags.append("Visual AI")
            features.append("Image Processing")
        elif any(word in content_lower for word in ['data', 'analytics', 'insight']):
            categories.append("Analytics")
            tags.append("Data Analysis")
            features.append("Analytics Dashboard")
        
        return {
            'short_description': short_desc,
            'description': long_desc,
            'key_features': features[:5],
            'use_cases': use_cases[:3],
            'categories': categories[:2],
            'tags': tags[:4],
            'pricing_model': 'FREEMIUM',
            'target_audience': ['Business Professionals', 'Teams'],
            'has_free_tier': True,
            'api_access': False,
            'mobile_support': False
        }
    
    def _build_validated_entity(self, name: str, url: str, website_data: dict, enhanced_data: dict) -> dict:
        """Build entity with proper validation"""
        
        # Get entity type
        entity_type = self.entity_detector.detect_entity_type(name, url)
        entity_type_id = self._get_entity_type_id(entity_type)
        
        # Map taxonomy with UUID validation
        category_ids = self.taxonomy_service.map_categories(enhanced_data.get('categories', []))
        tag_ids = self.taxonomy_service.map_tags(enhanced_data.get('tags', []))
        feature_ids = self.taxonomy_service.map_features(enhanced_data.get('key_features', []))
        
        # Validate UUIDs and filter invalid ones
        def is_valid_uuid(uuid_string):
            try:
                import uuid
                uuid.UUID(uuid_string)
                return True
            except (ValueError, TypeError):
                return False
        
        category_ids = [id for id in category_ids if is_valid_uuid(id)]
        tag_ids = [id for id in tag_ids if is_valid_uuid(id)]
        feature_ids = [id for id in feature_ids if is_valid_uuid(id)]
        
        # Ensure required fields
        if not category_ids and self.taxonomy_service.categories_map:
            first_cat = list(self.taxonomy_service.categories_map.values())[0]
            category_ids = [first_cat['id']]
        
        if not tag_ids:
            # Add default tags
            default_tags = self.taxonomy_service.map_tags(['AI-Powered'])
            if default_tags:
                tag_ids = [id for id in default_tags if is_valid_uuid(id)]
        
        # Validate and clean strings
        short_desc = str(enhanced_data.get('short_description', f"{name} - AI-powered solution"))[:150]
        long_desc = str(enhanced_data.get('description', f"{name} provides professional AI capabilities."))[:400]
        
        # Build entity
        entity = {
            'name': name,
            'website_url': url,
            'entity_type_id': entity_type_id,
            'short_description': short_desc,
            'description': long_desc,
            'category_ids': category_ids,
            'tag_ids': tag_ids if tag_ids else [],
            'feature_ids': feature_ids,
            'meta_title': f"{name} | AI Navigator",
            'meta_description': short_desc,
            'ref_link': url,
            'affiliate_status': 'NONE',
            'status': 'PENDING'
        }
        
        # Add optional fields
        if website_data.get('logo_url'):
            entity['logo_url'] = website_data['logo_url']
        
        if website_data.get('social_links'):
            entity['social_links'] = website_data['social_links']
        
        if enhanced_data.get('founded_year'):
            entity['founded_year'] = enhanced_data['founded_year']
        
        if enhanced_data.get('employee_count_range'):
            entity['employee_count_range'] = enhanced_data['employee_count_range']
        
        # Build tool details with validation
        entity['tool_details'] = {
            'learning_curve': 'MEDIUM',
            'key_features': enhanced_data.get('key_features', ['Professional Tools'])[:10],
            'has_free_tier': enhanced_data.get('has_free_tier', True),
            'use_cases': enhanced_data.get('use_cases', ['Business Applications'])[:5],
            'pricing_model': enhanced_data.get('pricing_model', 'FREEMIUM'),
            'target_audience': enhanced_data.get('target_audience', ['Business Professionals'])[:5],
            'mobile_support': enhanced_data.get('mobile_support', False),
            'api_access': enhanced_data.get('api_access', False),
            'customization_level': 'Medium',
            'trial_available': True,
            'demo_available': False,
            'open_source': False,
            'support_channels': ['Email', 'Documentation'],
            'integrations': enhanced_data.get('integrations', ['Standard APIs'])[:8],
            'supported_os': enhanced_data.get('supported_os', ['Web Browser'])[:5]
        }
        
        return entity
    
    def _get_entity_type_id(self, entity_type_slug: str) -> str:
        """Get entity type ID from API"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entity-types",
                headers=self.client._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entity_types = response.json()
                for et in entity_types:
                    if et.get('slug') == entity_type_slug:
                        return et.get('id')
            
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"  # ai-tool fallback
            
        except Exception as e:
            logger.error(f"Error getting entity type ID: {str(e)}")
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"
    
    def _create_entity(self, entity_data: dict) -> bool:
        """Create new entity"""
        try:
            result = self.client.create_entity(entity_data)
            return bool(result)
        except Exception as e:
            logger.error(f"Error creating entity: {str(e)}")
            return False
    
    def _update_entity(self, entity_id: str, entity_data: dict) -> bool:
        """Update existing entity"""
        try:
            update_data = entity_data.copy()
            update_data.pop('name', None)  # Don't change name
            
            response = requests.patch(
                f"{self.client.base_url}/entities/{entity_id}",
                headers=self.client._get_headers(),
                json=update_data,
                timeout=30
            )
            
            return response.status_code in [200, 201]
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False
    
    def _get_title(self, soup):
        title = soup.find('title')
        return title.get_text().strip() if title else ""
    
    def _get_meta_description(self, soup):
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '').strip() if meta_desc else ""
    
    def _extract_logo(self, soup, base_url):
        selectors = [
            'img[alt*="logo" i]', '.logo img', 'header img', '.navbar img',
            'img[src*="logo" i]', 'link[rel="apple-touch-icon"]', 'link[rel="icon"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                src = element.get('src') or element.get('href')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    if src.startswith('http'):
                        return src
        
        # Fallback
        domain = urlparse(base_url).netloc
        return f"https://www.google.com/s2/favicons?sz=128&domain={domain}"
    
    def _extract_social_links(self, page_text):
        patterns = {
            'twitter': r'twitter\.com/([a-zA-Z0-9_]{1,15})',
            'linkedin': r'linkedin\.com/company/([a-zA-Z0-9\-]{1,50})',
            'github': r'github\.com/([a-zA-Z0-9\-_]{1,39})'
        }
        
        social_links = {}
        for platform, pattern in patterns.items():
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                handle = matches[0]
                if handle and not any(skip in handle.lower() for skip in ['home', 'login', 'about']):
                    social_links[platform] = handle
        
        return social_links
    
    def _extract_headings(self, soup):
        headings = []
        for h in soup.find_all(['h1', 'h2', 'h3']):
            text = h.get_text().strip()
            if text and 5 < len(text) < 100:
                headings.append(text)
        return headings[:10]
    
    def _extract_paragraphs(self, soup):
        paragraphs = []
        for p in soup.find_all('p'):
            text = p.get_text().strip()
            if text and 50 < len(text) < 500:
                paragraphs.append(text)
        return paragraphs[:5]

def main():
    """Test multi-source processor"""
    
    XAI_API_KEY = "xai-O8fbPKvuJmwHrxeujsAXdUyTKKfzvv6MFw8Is3kEMUYwX7hKhjkkLjzSC91ouMe39ft0"
    PERPLEXITY_API_KEY = "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"
    
    processor = MultiSourceProcessor(XAI_API_KEY, PERPLEXITY_API_KEY)
    
    # Test Toolify scraping
    logger.info("🚀 Testing multi-source AI tool discovery...")
    
    # Try to scrape 10 tools from Toolify
    toolify_tools = processor.scrape_toolify_tools(max_tools=10)
    
    if toolify_tools:
        logger.info(f"✅ Found {len(toolify_tools)} tools from Toolify")
        
        # Process first 3 tools
        success_count = 0
        for i, tool in enumerate(toolify_tools[:3]):
            logger.info(f"\n{'='*60}")
            logger.info(f"Processing Toolify tool {i+1}/3")
            
            success = processor.process_tool(tool)
            if success:
                success_count += 1
        
        logger.info(f"\n🎉 TOOLIFY SUCCESS RATE: {success_count}/3 tools ({(success_count/3)*100:.1f}%)")
    else:
        logger.warning("❌ No tools found from Toolify")

if __name__ == "__main__":
    main()